{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 29040, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 29040, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 29040, "tid": 656078, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 29040, "tid": 656078, "ts": 1756660801279722, "dur": 1132, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 29040, "tid": 656078, "ts": 1756660801285625, "dur": 1075, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 29040, "tid": 21474836480, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 29040, "tid": 21474836480, "ts": **********224407, "dur": 38168, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": **********262576, "dur": 2006878, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": **********262587, "dur": 48, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": **********262637, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": **********262639, "dur": 164819, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": **********427467, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": **********427471, "dur": 63, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": **********427538, "dur": 19, "ph": "X", "name": "ProcessMessages 41", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": **********427558, "dur": 4014, "ph": "X", "name": "ReadAsync 41", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": **********431582, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": **********431586, "dur": 296, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": **********431885, "dur": 12, "ph": "X", "name": "ProcessMessages 13977", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": **********431898, "dur": 63, "ph": "X", "name": "ReadAsync 13977", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": **********431964, "dur": 2, "ph": "X", "name": "ProcessMessages 1575", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": **********431967, "dur": 44, "ph": "X", "name": "ReadAsync 1575", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": **********432014, "dur": 1, "ph": "X", "name": "ProcessMessages 613", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": **********432016, "dur": 39, "ph": "X", "name": "ReadAsync 613", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": **********432057, "dur": 1, "ph": "X", "name": "ProcessMessages 493", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": **********432060, "dur": 36, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": **********432099, "dur": 1, "ph": "X", "name": "ProcessMessages 212", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": **********432101, "dur": 39, "ph": "X", "name": "ReadAsync 212", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": **********432142, "dur": 1, "ph": "X", "name": "ProcessMessages 771", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": **********432144, "dur": 607, "ph": "X", "name": "ReadAsync 771", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": **********432755, "dur": 2, "ph": "X", "name": "ProcessMessages 381", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": **********432758, "dur": 271, "ph": "X", "name": "ReadAsync 381", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": **********433033, "dur": 8, "ph": "X", "name": "ProcessMessages 9079", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": **********433042, "dur": 69, "ph": "X", "name": "ReadAsync 9079", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": **********433113, "dur": 2, "ph": "X", "name": "ProcessMessages 2078", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": **********433116, "dur": 68, "ph": "X", "name": "ReadAsync 2078", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": **********433187, "dur": 1, "ph": "X", "name": "ProcessMessages 633", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": **********433189, "dur": 2792, "ph": "X", "name": "ReadAsync 633", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": **********435988, "dur": 3, "ph": "X", "name": "ProcessMessages 925", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": **********435993, "dur": 674, "ph": "X", "name": "ReadAsync 925", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": **********436672, "dur": 21, "ph": "X", "name": "ProcessMessages 20516", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": **********436694, "dur": 272, "ph": "X", "name": "ReadAsync 20516", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": **********436970, "dur": 5, "ph": "X", "name": "ProcessMessages 3774", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": **********436976, "dur": 76, "ph": "X", "name": "ReadAsync 3774", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": **********437055, "dur": 3, "ph": "X", "name": "ProcessMessages 2792", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": **********437059, "dur": 51, "ph": "X", "name": "ReadAsync 2792", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": **********437112, "dur": 1, "ph": "X", "name": "ProcessMessages 465", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": **********437114, "dur": 39, "ph": "X", "name": "ReadAsync 465", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": **********437155, "dur": 1, "ph": "X", "name": "ProcessMessages 451", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": **********437157, "dur": 52, "ph": "X", "name": "ReadAsync 451", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": **********437212, "dur": 1, "ph": "X", "name": "ProcessMessages 747", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": **********437213, "dur": 42, "ph": "X", "name": "ReadAsync 747", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": **********437258, "dur": 1, "ph": "X", "name": "ProcessMessages 463", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": **********437260, "dur": 232, "ph": "X", "name": "ReadAsync 463", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": **********437496, "dur": 1, "ph": "X", "name": "ProcessMessages 638", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": **********437498, "dur": 85, "ph": "X", "name": "ReadAsync 638", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": **********437586, "dur": 3, "ph": "X", "name": "ProcessMessages 3068", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": **********437590, "dur": 53, "ph": "X", "name": "ReadAsync 3068", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": **********437646, "dur": 1, "ph": "X", "name": "ProcessMessages 526", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": **********437648, "dur": 47, "ph": "X", "name": "ReadAsync 526", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": **********437697, "dur": 1, "ph": "X", "name": "ProcessMessages 618", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": **********437699, "dur": 36, "ph": "X", "name": "ReadAsync 618", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": **********437739, "dur": 1, "ph": "X", "name": "ProcessMessages 261", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": **********437741, "dur": 52, "ph": "X", "name": "ReadAsync 261", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": **********437796, "dur": 1, "ph": "X", "name": "ProcessMessages 291", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": **********437798, "dur": 40, "ph": "X", "name": "ReadAsync 291", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": **********437840, "dur": 1, "ph": "X", "name": "ProcessMessages 355", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": **********437842, "dur": 1677, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": **********439526, "dur": 3, "ph": "X", "name": "ProcessMessages 669", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": **********439533, "dur": 298, "ph": "X", "name": "ReadAsync 669", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": **********439835, "dur": 14, "ph": "X", "name": "ProcessMessages 14656", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": **********439850, "dur": 63, "ph": "X", "name": "ReadAsync 14656", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": **********439916, "dur": 1, "ph": "X", "name": "ProcessMessages 1361", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": **********439920, "dur": 51, "ph": "X", "name": "ReadAsync 1361", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": **********439973, "dur": 1, "ph": "X", "name": "ProcessMessages 536", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": **********439975, "dur": 42, "ph": "X", "name": "ReadAsync 536", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": **********440019, "dur": 1, "ph": "X", "name": "ProcessMessages 418", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": **********440021, "dur": 32, "ph": "X", "name": "ReadAsync 418", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": **********440057, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": **********440096, "dur": 626, "ph": "X", "name": "ReadAsync 321", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": **********440726, "dur": 1, "ph": "X", "name": "ProcessMessages 1", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": **********440728, "dur": 274, "ph": "X", "name": "ReadAsync 1", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": **********441006, "dur": 6, "ph": "X", "name": "ProcessMessages 6884", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": **********441013, "dur": 72, "ph": "X", "name": "ReadAsync 6884", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": **********441088, "dur": 2, "ph": "X", "name": "ProcessMessages 2765", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": **********441094, "dur": 46, "ph": "X", "name": "ReadAsync 2765", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": **********441143, "dur": 1, "ph": "X", "name": "ProcessMessages 701", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": **********441145, "dur": 41, "ph": "X", "name": "ReadAsync 701", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": **********441188, "dur": 1, "ph": "X", "name": "ProcessMessages 346", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": **********441190, "dur": 246, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": **********441438, "dur": 1, "ph": "X", "name": "ProcessMessages 721", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": **********441441, "dur": 79, "ph": "X", "name": "ReadAsync 721", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": **********441522, "dur": 3, "ph": "X", "name": "ProcessMessages 3354", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": **********441526, "dur": 52, "ph": "X", "name": "ReadAsync 3354", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": **********441580, "dur": 1, "ph": "X", "name": "ProcessMessages 399", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": **********441582, "dur": 38, "ph": "X", "name": "ReadAsync 399", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": **********441622, "dur": 1, "ph": "X", "name": "ProcessMessages 264", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": **********441624, "dur": 47, "ph": "X", "name": "ReadAsync 264", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": **********441673, "dur": 1, "ph": "X", "name": "ProcessMessages 454", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": **********441674, "dur": 40, "ph": "X", "name": "ReadAsync 454", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": **********441716, "dur": 1, "ph": "X", "name": "ProcessMessages 357", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": **********441718, "dur": 46, "ph": "X", "name": "ReadAsync 357", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": **********441767, "dur": 1, "ph": "X", "name": "ProcessMessages 462", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": **********441769, "dur": 45, "ph": "X", "name": "ReadAsync 462", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": **********441815, "dur": 1, "ph": "X", "name": "ProcessMessages 528", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": **********441817, "dur": 42, "ph": "X", "name": "ReadAsync 528", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": **********441863, "dur": 1, "ph": "X", "name": "ProcessMessages 465", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": **********441865, "dur": 44, "ph": "X", "name": "ReadAsync 465", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": **********441911, "dur": 1, "ph": "X", "name": "ProcessMessages 410", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": **********441913, "dur": 40, "ph": "X", "name": "ReadAsync 410", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": **********441955, "dur": 1, "ph": "X", "name": "ProcessMessages 252", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": **********441957, "dur": 49, "ph": "X", "name": "ReadAsync 252", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": **********442009, "dur": 1, "ph": "X", "name": "ProcessMessages 532", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": **********442010, "dur": 45, "ph": "X", "name": "ReadAsync 532", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": **********442058, "dur": 1, "ph": "X", "name": "ProcessMessages 533", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": **********442060, "dur": 38, "ph": "X", "name": "ReadAsync 533", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": **********442100, "dur": 1, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": **********442101, "dur": 43, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": **********442149, "dur": 40, "ph": "X", "name": "ReadAsync 429", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": **********442191, "dur": 1, "ph": "X", "name": "ProcessMessages 365", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": **********442193, "dur": 40, "ph": "X", "name": "ReadAsync 365", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": **********442235, "dur": 1, "ph": "X", "name": "ProcessMessages 330", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": **********442237, "dur": 44, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": **********442283, "dur": 1, "ph": "X", "name": "ProcessMessages 435", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": **********442285, "dur": 61, "ph": "X", "name": "ReadAsync 435", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": **********442349, "dur": 1, "ph": "X", "name": "ProcessMessages 422", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": **********442352, "dur": 54, "ph": "X", "name": "ReadAsync 422", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": **********442408, "dur": 1, "ph": "X", "name": "ProcessMessages 758", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": **********442410, "dur": 678, "ph": "X", "name": "ReadAsync 758", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": **********443094, "dur": 1, "ph": "X", "name": "ProcessMessages 360", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": **********443096, "dur": 167, "ph": "X", "name": "ReadAsync 360", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": **********443266, "dur": 9, "ph": "X", "name": "ProcessMessages 8723", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": **********443275, "dur": 44, "ph": "X", "name": "ReadAsync 8723", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": **********443322, "dur": 1, "ph": "X", "name": "ProcessMessages 372", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": **********443324, "dur": 43, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": **********443370, "dur": 1, "ph": "X", "name": "ProcessMessages 232", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": **********443371, "dur": 49, "ph": "X", "name": "ReadAsync 232", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": **********443423, "dur": 1, "ph": "X", "name": "ProcessMessages 516", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": **********443425, "dur": 65, "ph": "X", "name": "ReadAsync 516", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": **********443492, "dur": 1, "ph": "X", "name": "ProcessMessages 419", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": **********443494, "dur": 248, "ph": "X", "name": "ReadAsync 419", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": **********443746, "dur": 1, "ph": "X", "name": "ProcessMessages 444", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": **********443748, "dur": 68, "ph": "X", "name": "ReadAsync 444", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": **********443819, "dur": 3, "ph": "X", "name": "ProcessMessages 1793", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": **********443823, "dur": 55, "ph": "X", "name": "ReadAsync 1793", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": **********443880, "dur": 1, "ph": "X", "name": "ProcessMessages 449", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": **********443882, "dur": 36, "ph": "X", "name": "ReadAsync 449", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": **********443921, "dur": 1, "ph": "X", "name": "ProcessMessages 136", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": **********443922, "dur": 35, "ph": "X", "name": "ReadAsync 136", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": **********443961, "dur": 60, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": **********444025, "dur": 2, "ph": "X", "name": "ProcessMessages 404", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": **********444028, "dur": 54, "ph": "X", "name": "ReadAsync 404", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": **********444085, "dur": 1, "ph": "X", "name": "ProcessMessages 663", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": **********444087, "dur": 55, "ph": "X", "name": "ReadAsync 663", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": **********444145, "dur": 1, "ph": "X", "name": "ProcessMessages 572", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": **********444147, "dur": 59, "ph": "X", "name": "ReadAsync 572", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": **********444208, "dur": 1, "ph": "X", "name": "ProcessMessages 553", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": **********444213, "dur": 74, "ph": "X", "name": "ReadAsync 553", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": **********444290, "dur": 1, "ph": "X", "name": "ProcessMessages 531", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": **********444292, "dur": 34, "ph": "X", "name": "ReadAsync 531", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": **********444330, "dur": 1117, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": **********445450, "dur": 1, "ph": "X", "name": "ProcessMessages 318", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": **********445453, "dur": 164, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": **********445619, "dur": 8, "ph": "X", "name": "ProcessMessages 8428", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": **********445640, "dur": 51, "ph": "X", "name": "ReadAsync 8428", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": **********445694, "dur": 3, "ph": "X", "name": "ProcessMessages 873", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": **********445698, "dur": 42, "ph": "X", "name": "ReadAsync 873", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": **********445743, "dur": 1, "ph": "X", "name": "ProcessMessages 461", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": **********445745, "dur": 63, "ph": "X", "name": "ReadAsync 461", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": **********445812, "dur": 41, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": **********445855, "dur": 1, "ph": "X", "name": "ProcessMessages 452", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": **********445857, "dur": 41, "ph": "X", "name": "ReadAsync 452", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": **********445900, "dur": 1, "ph": "X", "name": "ProcessMessages 353", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": **********445902, "dur": 37, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": **********445942, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": **********445944, "dur": 32, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": **********445980, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": **********446018, "dur": 1, "ph": "X", "name": "ProcessMessages 209", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": **********446020, "dur": 40, "ph": "X", "name": "ReadAsync 209", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": **********446062, "dur": 1, "ph": "X", "name": "ProcessMessages 428", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": **********446064, "dur": 39, "ph": "X", "name": "ReadAsync 428", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": **********446105, "dur": 1, "ph": "X", "name": "ProcessMessages 325", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": **********446107, "dur": 277, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": **********446387, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": **********446389, "dur": 144, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": **********446536, "dur": 3, "ph": "X", "name": "ProcessMessages 2315", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": **********446542, "dur": 58, "ph": "X", "name": "ReadAsync 2315", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": **********446602, "dur": 1, "ph": "X", "name": "ProcessMessages 887", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": **********446605, "dur": 39, "ph": "X", "name": "ReadAsync 887", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": **********446646, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": **********446648, "dur": 60, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": **********446712, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": **********446758, "dur": 1, "ph": "X", "name": "ProcessMessages 358", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": **********446760, "dur": 45, "ph": "X", "name": "ReadAsync 358", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": **********446809, "dur": 1, "ph": "X", "name": "ProcessMessages 372", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": **********446811, "dur": 47, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": **********446861, "dur": 1, "ph": "X", "name": "ProcessMessages 452", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": **********446863, "dur": 210, "ph": "X", "name": "ReadAsync 452", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": **********447076, "dur": 1, "ph": "X", "name": "ProcessMessages 508", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": **********447078, "dur": 57, "ph": "X", "name": "ReadAsync 508", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": **********447138, "dur": 2, "ph": "X", "name": "ProcessMessages 1257", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": **********447141, "dur": 211, "ph": "X", "name": "ReadAsync 1257", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": **********447356, "dur": 1, "ph": "X", "name": "ProcessMessages 419", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": **********447358, "dur": 418, "ph": "X", "name": "ReadAsync 419", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": **********447780, "dur": 2, "ph": "X", "name": "ProcessMessages 1207", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": **********447783, "dur": 103, "ph": "X", "name": "ReadAsync 1207", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": **********447889, "dur": 4, "ph": "X", "name": "ProcessMessages 3750", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": **********447895, "dur": 36, "ph": "X", "name": "ReadAsync 3750", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": **********447934, "dur": 221, "ph": "X", "name": "ReadAsync 98", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": **********448158, "dur": 1, "ph": "X", "name": "ProcessMessages 410", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": **********448161, "dur": 1247, "ph": "X", "name": "ReadAsync 410", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": **********449413, "dur": 4, "ph": "X", "name": "ProcessMessages 2539", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": **********449418, "dur": 300, "ph": "X", "name": "ReadAsync 2539", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": **********449722, "dur": 9, "ph": "X", "name": "ProcessMessages 8334", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": **********449732, "dur": 944, "ph": "X", "name": "ReadAsync 8334", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": **********450680, "dur": 3, "ph": "X", "name": "ProcessMessages 1496", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": **********450684, "dur": 193, "ph": "X", "name": "ReadAsync 1496", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": **********450880, "dur": 8, "ph": "X", "name": "ProcessMessages 8364", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": **********450889, "dur": 10974, "ph": "X", "name": "ReadAsync 8364", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": **********461871, "dur": 3, "ph": "X", "name": "ProcessMessages 66", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": **********461875, "dur": 38631, "ph": "X", "name": "ReadAsync 66", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": **********500513, "dur": 50, "ph": "X", "name": "ProcessMessages 20483", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": **********500565, "dur": 27326, "ph": "X", "name": "ReadAsync 20483", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": **********527899, "dur": 50, "ph": "X", "name": "ProcessMessages 2406", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": **********527951, "dur": 12427, "ph": "X", "name": "ReadAsync 2406", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": **********540387, "dur": 6, "ph": "X", "name": "ProcessMessages 196", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": **********540395, "dur": 30578, "ph": "X", "name": "ReadAsync 196", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": **********570983, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": **********570989, "dur": 9536, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": **********580534, "dur": 7, "ph": "X", "name": "ProcessMessages 468", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": **********580542, "dur": 3899, "ph": "X", "name": "ReadAsync 468", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": **********584448, "dur": 6, "ph": "X", "name": "ProcessMessages 496", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": **********584455, "dur": 763, "ph": "X", "name": "ReadAsync 496", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": **********585224, "dur": 2, "ph": "X", "name": "ProcessMessages 208", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": **********585228, "dur": 55, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": **********585288, "dur": 388, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": **********585681, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": **********585712, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": **********585715, "dur": 637686, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": 1756660800223410, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": 1756660800223414, "dur": 12495, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": 1756660800235919, "dur": 41, "ph": "X", "name": "ProcessMessages 2125", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": 1756660800235963, "dur": 85238, "ph": "X", "name": "ReadAsync 2125", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": 1756660800321210, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": 1756660800321214, "dur": 57, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": 1756660800321277, "dur": 41, "ph": "X", "name": "ProcessMessages 365", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": 1756660800321320, "dur": 10835, "ph": "X", "name": "ReadAsync 365", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": 1756660800332164, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": 1756660800332169, "dur": 66, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": 1756660800332237, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": 1756660800332241, "dur": 3325, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": 1756660800335573, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": 1756660800335577, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": 1756660800335627, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": 1756660800335631, "dur": 479419, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": 1756660800815060, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": 1756660800815064, "dur": 5275, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": 1756660800820347, "dur": 31, "ph": "X", "name": "ProcessMessages 429", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": 1756660800820380, "dur": 14032, "ph": "X", "name": "ReadAsync 429", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": 1756660800834421, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": 1756660800834428, "dur": 3096, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": 1756660800837530, "dur": 44, "ph": "X", "name": "ProcessMessages 379", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": 1756660800837576, "dur": 12137, "ph": "X", "name": "ReadAsync 379", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": 1756660800849721, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": 1756660800849725, "dur": 3958, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": 1756660800853692, "dur": 29, "ph": "X", "name": "ProcessMessages 140", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": 1756660800853723, "dur": 356371, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": 1756660801210103, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": 1756660801210107, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": 1756660801210145, "dur": 25, "ph": "X", "name": "ProcessMessages 435", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": 1756660801210172, "dur": 37154, "ph": "X", "name": "ReadAsync 435", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": 1756660801247335, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": 1756660801247339, "dur": 74, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": 1756660801247416, "dur": 3, "ph": "X", "name": "ProcessMessages 28", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": 1756660801247420, "dur": 3126, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": 1756660801250552, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": 1756660801250556, "dur": 768, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": 1756660801251328, "dur": 23, "ph": "X", "name": "ProcessMessages 121", "args": {}}, {"pid": 29040, "tid": 21474836480, "ts": 1756660801251353, "dur": 18097, "ph": "X", "name": "ReadAsync 121", "args": {}}, {"pid": 29040, "tid": 656078, "ts": 1756660801286705, "dur": 785, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 29040, "tid": 17179869184, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 29040, "tid": 17179869184, "ts": **********224327, "dur": 35, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 29040, "tid": 17179869184, "ts": **********224363, "dur": 38211, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 29040, "tid": 17179869184, "ts": **********262576, "dur": 64, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 29040, "tid": 656078, "ts": 1756660801287493, "dur": 7, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 29040, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 29040, "tid": 1, "ts": 1756660796673689, "dur": 11311, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 29040, "tid": 1, "ts": 1756660796685006, "dur": 136113, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 29040, "tid": 1, "ts": 1756660796821131, "dur": 72899, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 29040, "tid": 656078, "ts": 1756660801287502, "dur": 7, "ph": "X", "name": "", "args": {}}, {"pid": 29040, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796663644, "dur": 997, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796664645, "dur": 1692540, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796666098, "dur": 3927, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796670036, "dur": 3469, "ph": "X", "name": "ProcessMessages 5926", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796673513, "dur": 2627, "ph": "X", "name": "ReadAsync 5926", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796676148, "dur": 22, "ph": "X", "name": "ProcessMessages 20512", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796676172, "dur": 402, "ph": "X", "name": "ReadAsync 20512", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796676577, "dur": 21, "ph": "X", "name": "ProcessMessages 20408", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796676600, "dur": 51, "ph": "X", "name": "ReadAsync 20408", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796676653, "dur": 1, "ph": "X", "name": "ProcessMessages 751", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796676655, "dur": 1568, "ph": "X", "name": "ReadAsync 751", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796678512, "dur": 3, "ph": "X", "name": "ProcessMessages 292", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796678519, "dur": 747, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796679271, "dur": 20, "ph": "X", "name": "ProcessMessages 20065", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796679293, "dur": 90, "ph": "X", "name": "ReadAsync 20065", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796679385, "dur": 3, "ph": "X", "name": "ProcessMessages 2872", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796679389, "dur": 29, "ph": "X", "name": "ReadAsync 2872", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796679421, "dur": 30, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796679453, "dur": 1, "ph": "X", "name": "ProcessMessages 303", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796679455, "dur": 27, "ph": "X", "name": "ReadAsync 303", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796679485, "dur": 28, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796679516, "dur": 21, "ph": "X", "name": "ReadAsync 213", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796679541, "dur": 21, "ph": "X", "name": "ReadAsync 247", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796679565, "dur": 27, "ph": "X", "name": "ReadAsync 275", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796679595, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796679623, "dur": 18, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796679644, "dur": 311, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796679958, "dur": 3, "ph": "X", "name": "ProcessMessages 2557", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796679962, "dur": 40, "ph": "X", "name": "ReadAsync 2557", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796680003, "dur": 1, "ph": "X", "name": "ProcessMessages 462", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796680005, "dur": 33, "ph": "X", "name": "ReadAsync 462", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796680042, "dur": 28, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796680073, "dur": 21, "ph": "X", "name": "ReadAsync 299", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796680097, "dur": 19, "ph": "X", "name": "ReadAsync 82", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796680119, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796680148, "dur": 19, "ph": "X", "name": "ReadAsync 358", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796680171, "dur": 49, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796680222, "dur": 1, "ph": "X", "name": "ProcessMessages 383", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796680224, "dur": 48, "ph": "X", "name": "ReadAsync 383", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796680273, "dur": 1, "ph": "X", "name": "ProcessMessages 718", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796680275, "dur": 207, "ph": "X", "name": "ReadAsync 718", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796680487, "dur": 1, "ph": "X", "name": "ProcessMessages 515", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796680489, "dur": 64, "ph": "X", "name": "ReadAsync 515", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796680556, "dur": 2, "ph": "X", "name": "ProcessMessages 2389", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796680559, "dur": 23, "ph": "X", "name": "ReadAsync 2389", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796680585, "dur": 47, "ph": "X", "name": "ReadAsync 169", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796680636, "dur": 578, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796681218, "dur": 1, "ph": "X", "name": "ProcessMessages 575", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796681221, "dur": 759, "ph": "X", "name": "ReadAsync 575", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796681985, "dur": 8, "ph": "X", "name": "ProcessMessages 6711", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796681995, "dur": 275, "ph": "X", "name": "ReadAsync 6711", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796682274, "dur": 8, "ph": "X", "name": "ProcessMessages 8202", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796682283, "dur": 61, "ph": "X", "name": "ReadAsync 8202", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796682346, "dur": 2, "ph": "X", "name": "ProcessMessages 1736", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796682349, "dur": 34, "ph": "X", "name": "ReadAsync 1736", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796682386, "dur": 29, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796682418, "dur": 21, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796682443, "dur": 20, "ph": "X", "name": "ReadAsync 142", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796682465, "dur": 21, "ph": "X", "name": "ReadAsync 181", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796682489, "dur": 24, "ph": "X", "name": "ReadAsync 179", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796682517, "dur": 21, "ph": "X", "name": "ReadAsync 288", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796682540, "dur": 445, "ph": "X", "name": "ReadAsync 163", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796682990, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796682992, "dur": 8570, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796691571, "dur": 8, "ph": "X", "name": "ProcessMessages 4768", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796691581, "dur": 2483, "ph": "X", "name": "ReadAsync 4768", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796694071, "dur": 25, "ph": "X", "name": "ProcessMessages 20510", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796694098, "dur": 101, "ph": "X", "name": "ReadAsync 20510", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796694204, "dur": 4, "ph": "X", "name": "ProcessMessages 2400", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796694209, "dur": 79, "ph": "X", "name": "ReadAsync 2400", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796694292, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796694294, "dur": 63, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796694362, "dur": 1, "ph": "X", "name": "ProcessMessages 755", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796694364, "dur": 52, "ph": "X", "name": "ReadAsync 755", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796694419, "dur": 1, "ph": "X", "name": "ProcessMessages 520", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796694421, "dur": 43, "ph": "X", "name": "ReadAsync 520", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796694467, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796694469, "dur": 107, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796694580, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796694582, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796694625, "dur": 1, "ph": "X", "name": "ProcessMessages 314", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796694628, "dur": 47, "ph": "X", "name": "ReadAsync 314", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796694677, "dur": 1, "ph": "X", "name": "ProcessMessages 608", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796694679, "dur": 46, "ph": "X", "name": "ReadAsync 608", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796694729, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796694743, "dur": 44, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796694789, "dur": 1, "ph": "X", "name": "ProcessMessages 311", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796694792, "dur": 113, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796694910, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796694955, "dur": 1, "ph": "X", "name": "ProcessMessages 508", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796694957, "dur": 35, "ph": "X", "name": "ReadAsync 508", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796695009, "dur": 42, "ph": "X", "name": "ReadAsync 328", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796695056, "dur": 273, "ph": "X", "name": "ProcessMessages 258", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796695332, "dur": 60, "ph": "X", "name": "ReadAsync 258", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796695396, "dur": 2, "ph": "X", "name": "ProcessMessages 1084", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796695399, "dur": 50, "ph": "X", "name": "ReadAsync 1084", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796695452, "dur": 1, "ph": "X", "name": "ProcessMessages 347", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796695454, "dur": 44, "ph": "X", "name": "ReadAsync 347", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796695501, "dur": 1, "ph": "X", "name": "ProcessMessages 458", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796695503, "dur": 38, "ph": "X", "name": "ReadAsync 458", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796695544, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796695545, "dur": 168, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796695719, "dur": 57, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796695778, "dur": 8, "ph": "X", "name": "ProcessMessages 745", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796695790, "dur": 34, "ph": "X", "name": "ReadAsync 745", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796695826, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796695828, "dur": 98, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796695932, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796695976, "dur": 1, "ph": "X", "name": "ProcessMessages 448", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796695978, "dur": 36, "ph": "X", "name": "ReadAsync 448", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796696018, "dur": 1, "ph": "X", "name": "ProcessMessages 361", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796696020, "dur": 30, "ph": "X", "name": "ReadAsync 361", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796696053, "dur": 102, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796696160, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796696201, "dur": 1, "ph": "X", "name": "ProcessMessages 421", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796696203, "dur": 37, "ph": "X", "name": "ReadAsync 421", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796696245, "dur": 40, "ph": "X", "name": "ReadAsync 257", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796696300, "dur": 2, "ph": "X", "name": "ProcessMessages 395", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796696303, "dur": 83, "ph": "X", "name": "ReadAsync 395", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796696399, "dur": 2, "ph": "X", "name": "ProcessMessages 461", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796696412, "dur": 46, "ph": "X", "name": "ReadAsync 461", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796696461, "dur": 1, "ph": "X", "name": "ProcessMessages 595", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796696464, "dur": 21, "ph": "X", "name": "ReadAsync 595", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796696488, "dur": 141, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796696632, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796696634, "dur": 89, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796696727, "dur": 2, "ph": "X", "name": "ProcessMessages 430", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796696730, "dur": 85, "ph": "X", "name": "ReadAsync 430", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796696818, "dur": 1, "ph": "X", "name": "ProcessMessages 1250", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796696821, "dur": 39, "ph": "X", "name": "ReadAsync 1250", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796696862, "dur": 1, "ph": "X", "name": "ProcessMessages 293", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796696864, "dur": 33, "ph": "X", "name": "ReadAsync 293", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796696899, "dur": 52, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796696954, "dur": 1, "ph": "X", "name": "ProcessMessages 305", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796696955, "dur": 33, "ph": "X", "name": "ReadAsync 305", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796696992, "dur": 1, "ph": "X", "name": "ProcessMessages 86", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796696994, "dur": 44, "ph": "X", "name": "ReadAsync 86", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796697042, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796697044, "dur": 107, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796697155, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796697157, "dur": 59, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796697221, "dur": 1, "ph": "X", "name": "ProcessMessages 565", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796697223, "dur": 46, "ph": "X", "name": "ReadAsync 565", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796697273, "dur": 1, "ph": "X", "name": "ProcessMessages 358", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796697276, "dur": 42, "ph": "X", "name": "ReadAsync 358", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796697323, "dur": 1, "ph": "X", "name": "ProcessMessages 337", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796697325, "dur": 39, "ph": "X", "name": "ReadAsync 337", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796697367, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796697368, "dur": 91, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796697463, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796697465, "dur": 50, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796697518, "dur": 1, "ph": "X", "name": "ProcessMessages 577", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796697520, "dur": 45, "ph": "X", "name": "ReadAsync 577", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796697569, "dur": 1, "ph": "X", "name": "ProcessMessages 501", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796697571, "dur": 48, "ph": "X", "name": "ReadAsync 501", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796697622, "dur": 1, "ph": "X", "name": "ProcessMessages 492", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796697625, "dur": 49, "ph": "X", "name": "ReadAsync 492", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796697677, "dur": 1, "ph": "X", "name": "ProcessMessages 454", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796697680, "dur": 44, "ph": "X", "name": "ReadAsync 454", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796697726, "dur": 1, "ph": "X", "name": "ProcessMessages 556", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796697728, "dur": 41, "ph": "X", "name": "ReadAsync 556", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796697772, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796697774, "dur": 96, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796697875, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796697924, "dur": 1, "ph": "X", "name": "ProcessMessages 472", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796697926, "dur": 41, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796697969, "dur": 1, "ph": "X", "name": "ProcessMessages 459", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796697971, "dur": 33, "ph": "X", "name": "ReadAsync 459", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796698006, "dur": 1, "ph": "X", "name": "ProcessMessages 136", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796698007, "dur": 168, "ph": "X", "name": "ReadAsync 136", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796698180, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796698225, "dur": 1, "ph": "X", "name": "ProcessMessages 496", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796698228, "dur": 50, "ph": "X", "name": "ReadAsync 496", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796698281, "dur": 1, "ph": "X", "name": "ProcessMessages 503", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796698283, "dur": 44, "ph": "X", "name": "ReadAsync 503", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796698330, "dur": 1, "ph": "X", "name": "ProcessMessages 576", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796698333, "dur": 29, "ph": "X", "name": "ReadAsync 576", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796698365, "dur": 34, "ph": "X", "name": "ReadAsync 504", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796698401, "dur": 1, "ph": "X", "name": "ProcessMessages 115", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796698403, "dur": 43, "ph": "X", "name": "ReadAsync 115", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796698448, "dur": 1, "ph": "X", "name": "ProcessMessages 520", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796698450, "dur": 37, "ph": "X", "name": "ReadAsync 520", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796698491, "dur": 24, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796698517, "dur": 99, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796698621, "dur": 52, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796698677, "dur": 1, "ph": "X", "name": "ProcessMessages 556", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796698679, "dur": 39, "ph": "X", "name": "ReadAsync 556", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796698724, "dur": 79, "ph": "X", "name": "ReadAsync 142", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796698806, "dur": 1, "ph": "X", "name": "ProcessMessages 513", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796698810, "dur": 107, "ph": "X", "name": "ReadAsync 513", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796698921, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796698922, "dur": 51, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796698976, "dur": 1, "ph": "X", "name": "ProcessMessages 1034", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796698978, "dur": 66, "ph": "X", "name": "ReadAsync 1034", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796699046, "dur": 1, "ph": "X", "name": "ProcessMessages 143", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796699049, "dur": 46, "ph": "X", "name": "ReadAsync 143", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796699097, "dur": 50, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796699151, "dur": 1, "ph": "X", "name": "ProcessMessages 257", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796699153, "dur": 52, "ph": "X", "name": "ReadAsync 257", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796699249, "dur": 2, "ph": "X", "name": "ProcessMessages 907", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796699253, "dur": 51, "ph": "X", "name": "ReadAsync 907", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796699308, "dur": 25, "ph": "X", "name": "ProcessMessages 62", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796699335, "dur": 391, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796699729, "dur": 3, "ph": "X", "name": "ProcessMessages 2202", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796699734, "dur": 19, "ph": "X", "name": "ReadAsync 2202", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796699756, "dur": 11, "ph": "X", "name": "ReadAsync 233", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796699770, "dur": 14, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796699786, "dur": 23, "ph": "X", "name": "ReadAsync 157", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796699814, "dur": 93, "ph": "X", "name": "ReadAsync 250", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796699910, "dur": 2, "ph": "X", "name": "ProcessMessages 348", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796699914, "dur": 16, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796699932, "dur": 54, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796699991, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796700040, "dur": 1, "ph": "X", "name": "ProcessMessages 505", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796700042, "dur": 43, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796700089, "dur": 1, "ph": "X", "name": "ProcessMessages 459", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796700092, "dur": 37, "ph": "X", "name": "ReadAsync 459", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796700131, "dur": 1, "ph": "X", "name": "ProcessMessages 138", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796700132, "dur": 99, "ph": "X", "name": "ReadAsync 138", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796700236, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796700277, "dur": 1, "ph": "X", "name": "ProcessMessages 523", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796700279, "dur": 4298, "ph": "X", "name": "ReadAsync 523", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796704584, "dur": 3, "ph": "X", "name": "ProcessMessages 400", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796704588, "dur": 5398, "ph": "X", "name": "ReadAsync 400", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796709995, "dur": 24, "ph": "X", "name": "ProcessMessages 20492", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796710021, "dur": 157, "ph": "X", "name": "ReadAsync 20492", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796710181, "dur": 7, "ph": "X", "name": "ProcessMessages 5378", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796710190, "dur": 6249, "ph": "X", "name": "ReadAsync 5378", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796716448, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796716451, "dur": 3018, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796719477, "dur": 943, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796720425, "dur": 6049, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796726485, "dur": 15, "ph": "X", "name": "ProcessMessages 1488", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796726501, "dur": 69730, "ph": "X", "name": "ReadAsync 1488", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796796242, "dur": 14, "ph": "X", "name": "ProcessMessages 1456", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796796257, "dur": 150, "ph": "X", "name": "ReadAsync 1456", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796796409, "dur": 6552, "ph": "X", "name": "ProcessMessages 3300", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796802968, "dur": 17557, "ph": "X", "name": "ReadAsync 3300", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796820531, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796820535, "dur": 20922, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796841465, "dur": 5, "ph": "X", "name": "ProcessMessages 272", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796841472, "dur": 3308, "ph": "X", "name": "ReadAsync 272", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796844784, "dur": 7, "ph": "X", "name": "ProcessMessages 672", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796844793, "dur": 4576, "ph": "X", "name": "ReadAsync 672", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796849373, "dur": 3, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796849377, "dur": 14276, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796863661, "dur": 6, "ph": "X", "name": "ProcessMessages 164", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660796863669, "dur": 1370911, "ph": "X", "name": "ReadAsync 164", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660798234589, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660798234592, "dur": 208, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660798234804, "dur": 9371, "ph": "X", "name": "ProcessMessages 365", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660798244182, "dur": 74912, "ph": "X", "name": "ReadAsync 365", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660798319102, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660798319107, "dur": 2554, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660798321670, "dur": 242, "ph": "X", "name": "ProcessMessages 205", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660798321915, "dur": 11721, "ph": "X", "name": "ReadAsync 205", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660798333643, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660798333647, "dur": 661, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660798334313, "dur": 402, "ph": "X", "name": "ProcessMessages 13", "args": {}}, {"pid": 29040, "tid": 12884901888, "ts": 1756660798334720, "dur": 22373, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 29040, "tid": 656078, "ts": 1756660801287511, "dur": 445, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 29040, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 29040, "tid": 8589934592, "ts": 1756660796659460, "dur": 234607, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 29040, "tid": 8589934592, "ts": 1756660796894070, "dur": 6, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 29040, "tid": 8589934592, "ts": 1756660796894077, "dur": 14240, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 29040, "tid": 656078, "ts": 1756660801287958, "dur": 6, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 29040, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 29040, "tid": 4294967296, "ts": 1756660796554491, "dur": 1803908, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 29040, "tid": 4294967296, "ts": 1756660796563346, "dur": 14323, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 29040, "tid": 4294967296, "ts": 1756660798358556, "dur": 852177, "ph": "X", "name": "await ExecuteBuildProgram", "args": {}}, {"pid": 29040, "tid": 4294967296, "ts": **********210959, "dur": 2058540, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 29040, "tid": 4294967296, "ts": **********211087, "dur": 13201, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 29040, "tid": 4294967296, "ts": 1756660801269511, "dur": 7696, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 29040, "tid": 4294967296, "ts": 1756660801274305, "dur": 41, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 29040, "tid": 4294967296, "ts": 1756660801277214, "dur": 16, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 29040, "tid": 656078, "ts": 1756660801287966, "dur": 25, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": **********263306, "dur": 167046, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": **********430358, "dur": 364, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": **********430883, "dur": 89, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": **********430972, "dur": 198, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": **********432324, "dur": 225, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_DC3D92D303F07AD8.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": **********433496, "dur": 138, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_D11517B46DED24AD.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": **********433782, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WindowsStandalone.Extensions.dll_EA6DB8F3FED6BDB6.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": **********433921, "dur": 121, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.YamlDotNet.dll_20FDB8B7CF876BCD.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": **********434344, "dur": 62, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.NVIDIAModule.dll_7BE3F413C29F6EC1.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": **********434411, "dur": 157, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TestRunner.ref.dll_193EC4CE382CBFB3.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": **********434572, "dur": 70, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": **********436323, "dur": 770, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.rsp"}}, {"pid": 12345, "tid": 0, "ts": **********437440, "dur": 80, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 0, "ts": **********437712, "dur": 66, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.rsp2"}}, {"pid": 12345, "tid": 0, "ts": **********438247, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Searcher.Editor.ref.dll_12B7E1785E41BE0E.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": **********440316, "dur": 210, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Flow.Editor.ref.dll_6AF951BE66A4493E.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": **********440759, "dur": 158, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.State.ref.dll_D91762B7076BE462.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": **********441461, "dur": 112, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.RenderPipelines.Core.ShaderLibrary.ref.dll_7F2C410A434F5518.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": **********441748, "dur": 63, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": **********442170, "dur": 72, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": **********443833, "dur": 158, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualStudio.Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": **********444471, "dur": 57, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEditor.UI.dll"}}, {"pid": 12345, "tid": 0, "ts": **********446224, "dur": 121, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Searcher.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": **********447127, "dur": 67, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/VTabs.rsp"}}, {"pid": 12345, "tid": 0, "ts": **********448090, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Burst.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": **********449215, "dur": 135, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/9703144790800738880.rsp"}}, {"pid": 12345, "tid": 0, "ts": **********450165, "dur": 155, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Universal.Runtime.dll"}}, {"pid": 12345, "tid": 0, "ts": **********450470, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": **********451419, "dur": 156, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/1723042715960067801.rsp"}}, {"pid": 12345, "tid": 0, "ts": **********431192, "dur": 22220, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": **********453427, "dur": 1796922, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1756660801250555, "dur": 88, "ph": "X", "name": "BuildQueueDestroyTail", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1756660801250682, "dur": 3634, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": **********432233, "dur": 21205, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": **********453443, "dur": 5502, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": **********459160, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": **********459600, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 1, "ts": **********460225, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.dll_62273FC84B3E0F2D.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": **********462320, "dur": 961, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 1, "ts": **********463820, "dur": 348, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": **********466989, "dur": 16272, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.rsp"}}, {"pid": 12345, "tid": 1, "ts": **********483263, "dur": 736, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": **********483999, "dur": 700, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": **********484700, "dur": 2184, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": **********486885, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": **********487042, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": **********487994, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.mathematics@1.2.6\\Unity.Mathematics\\double2x3.gen.cs"}}, {"pid": 12345, "tid": 1, "ts": **********488046, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.mathematics@1.2.6\\Unity.Mathematics\\double2x4.gen.cs"}}, {"pid": 12345, "tid": 1, "ts": **********488257, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.mathematics@1.2.6\\Unity.Mathematics\\double4.gen.cs"}}, {"pid": 12345, "tid": 1, "ts": **********488389, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.mathematics@1.2.6\\Unity.Mathematics\\double4x4.gen.cs"}}, {"pid": 12345, "tid": 1, "ts": **********488988, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.mathematics@1.2.6\\Unity.Mathematics\\half.cs"}}, {"pid": 12345, "tid": 1, "ts": **********489253, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.mathematics@1.2.6\\Unity.Mathematics\\int2x2.gen.cs"}}, {"pid": 12345, "tid": 1, "ts": **********487155, "dur": 3727, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": **********490883, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": **********490990, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": **********491108, "dur": 479, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": **********491588, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": **********491733, "dur": 524, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": **********492258, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": **********492377, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": **********492490, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": **********492722, "dur": 453, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program Files (x86)\\Unity\\Unity 3D\\2022.3.33f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VRModule.dll"}}, {"pid": 12345, "tid": 1, "ts": **********493410, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal-config@14.0.10\\Runtime\\ShaderConfig.cs"}}, {"pid": 12345, "tid": 1, "ts": **********492608, "dur": 861, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": **********493470, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": **********493646, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": **********493767, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": **********495599, "dur": 272, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@1.7.7\\Runtime\\Extensions\\TrackExtensions.cs"}}, {"pid": 12345, "tid": 1, "ts": **********495932, "dur": 347, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@1.7.7\\Runtime\\ILayerable.cs"}}, {"pid": 12345, "tid": 1, "ts": **********496967, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@1.7.7\\Runtime\\Utilities\\Extrapolation.cs"}}, {"pid": 12345, "tid": 1, "ts": **********497141, "dur": 85, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@1.7.7\\Runtime\\Utilities\\IPropertyPreview.cs"}}, {"pid": 12345, "tid": 1, "ts": **********493882, "dur": 3567, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": **********497450, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": **********498170, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.textmeshpro@3.0.9\\Scripts\\Runtime\\TextContainer.cs"}}, {"pid": 12345, "tid": 1, "ts": **********498223, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.textmeshpro@3.0.9\\Scripts\\Runtime\\TextMeshPro.cs"}}, {"pid": 12345, "tid": 1, "ts": **********499668, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.textmeshpro@3.0.9\\Scripts\\Runtime\\TMP_SubMesh.cs"}}, {"pid": 12345, "tid": 1, "ts": **********499727, "dur": 75, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.textmeshpro@3.0.9\\Scripts\\Runtime\\TMP_SubMeshUI.cs"}}, {"pid": 12345, "tid": 1, "ts": **********499876, "dur": 113, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.textmeshpro@3.0.9\\Scripts\\Runtime\\TMP_TextElement_Legacy.cs"}}, {"pid": 12345, "tid": 1, "ts": **********497564, "dur": 2672, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": **********500237, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": **********500429, "dur": 625, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 1, "ts": **********501061, "dur": 1600, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": **********502668, "dur": 157, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": **********502835, "dur": 667042, "ph": "X", "name": "ILPP-Configuration", "args": {"detail": "Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 1, "ts": 1756660800182909, "dur": 1724, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1756660800184634, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756660800184738, "dur": 1739, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Config.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1756660800186478, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756660800186605, "dur": 1653, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1756660800188259, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756660800189052, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program Files (x86)\\Unity\\Unity 3D\\2022.3.33f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Http.Connections.dll"}}, {"pid": 12345, "tid": 1, "ts": 1756660800188360, "dur": 1740, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1756660800190101, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756660800190232, "dur": 1603, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1756660800191835, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756660800191944, "dur": 1621, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1756660800193566, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756660800193661, "dur": 1622, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/PPv2URPConverters.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1756660800195284, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756660800195379, "dur": 1615, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1756660800196995, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756660800197085, "dur": 1606, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1756660800198692, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756660800198776, "dur": 1627, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1756660800200403, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756660800201948, "dur": 746, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program Files (x86)\\Unity\\Unity 3D\\2022.3.33f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Linq.Parallel.dll"}}, {"pid": 12345, "tid": 1, "ts": 1756660800200505, "dur": 2359, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Searcher.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1756660800202865, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756660800203007, "dur": 1655, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1756660800204663, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756660800204761, "dur": 1851, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.SettingsProvider.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1756660800206612, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756660800208242, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756660800208415, "dur": 123272, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756660800331691, "dur": 300, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 1, "ts": 1756660800331689, "dur": 2008, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1756660800335905, "dur": 320, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756660800336303, "dur": 466867, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1756660800843497, "dur": 294, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 1, "ts": 1756660800843496, "dur": 296, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 1, "ts": 1756660800843843, "dur": 3122, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 1, "ts": 1756660800846969, "dur": 403285, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********432409, "dur": 21038, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********453471, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********453627, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program Files (x86)\\Unity\\Unity 3D\\2022.3.33f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityConnectModule.dll"}}, {"pid": 12345, "tid": 2, "ts": **********453610, "dur": 115, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_CEBA40613B934AEB.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": **********453963, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_E5A8879E05CAF719.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": **********454447, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********454516, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program Files (x86)\\Unity\\Unity 3D\\2022.3.33f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.SpriteMaskModule.dll"}}, {"pid": 12345, "tid": 2, "ts": **********454515, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_D67657A67A1FD2C7.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": **********454581, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********454655, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program Files (x86)\\Unity\\Unity 3D\\2022.3.33f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ScreenCaptureModule.dll"}}, {"pid": 12345, "tid": 2, "ts": **********454653, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ScreenCaptureModule.dll_EE6422BA6596CB08.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": **********454711, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********454772, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_C5CC71DD8C50CC20.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": **********455563, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********455627, "dur": 92, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program Files (x86)\\Unity\\Unity 3D\\2022.3.33f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.TextCoreTextEngineModule.dll"}}, {"pid": 12345, "tid": 2, "ts": **********455625, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_F37D65FA45EEBDE6.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": **********457543, "dur": 684, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program Files (x86)\\Unity\\Unity 3D\\2022.3.33f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.XRModule.dll"}}, {"pid": 12345, "tid": 2, "ts": **********457542, "dur": 687, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_3531AA43DBDE9020.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": **********458285, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": **********459147, "dur": 78, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program Files (x86)\\Unity\\Unity 3D\\2022.3.33f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\Microsoft.Win32.Primitives.dll"}}, {"pid": 12345, "tid": 2, "ts": **********459226, "dur": 3123, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program Files (x86)\\Unity\\Unity 3D\\2022.3.33f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.AppContext.dll"}}, {"pid": 12345, "tid": 2, "ts": **********462461, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program Files (x86)\\Unity\\Unity 3D\\2022.3.33f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.Sockets.dll"}}, {"pid": 12345, "tid": 2, "ts": **********462521, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program Files (x86)\\Unity\\Unity 3D\\2022.3.33f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.WebHeaderCollection.dll"}}, {"pid": 12345, "tid": 2, "ts": **********462578, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program Files (x86)\\Unity\\Unity 3D\\2022.3.33f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.WebSockets.Client.dll"}}, {"pid": 12345, "tid": 2, "ts": **********462679, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program Files (x86)\\Unity\\Unity 3D\\2022.3.33f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Numerics.Vectors.dll"}}, {"pid": 12345, "tid": 2, "ts": **********462733, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program Files (x86)\\Unity\\Unity 3D\\2022.3.33f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ObjectModel.dll"}}, {"pid": 12345, "tid": 2, "ts": **********462791, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program Files (x86)\\Unity\\Unity 3D\\2022.3.33f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.DispatchProxy.dll"}}, {"pid": 12345, "tid": 2, "ts": **********462897, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program Files (x86)\\Unity\\Unity 3D\\2022.3.33f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.Emit.dll"}}, {"pid": 12345, "tid": 2, "ts": **********463001, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program Files (x86)\\Unity\\Unity 3D\\2022.3.33f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.Emit.Lightweight.dll"}}, {"pid": 12345, "tid": 2, "ts": **********463261, "dur": 581, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program Files (x86)\\Unity\\Unity 3D\\2022.3.33f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.CompilerServices.VisualC.dll"}}, {"pid": 12345, "tid": 2, "ts": **********464119, "dur": 705, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program Files (x86)\\Unity\\Unity 3D\\2022.3.33f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Threading.dll"}}, {"pid": 12345, "tid": 2, "ts": **********464852, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ext.nunit@1.0.6\\net35\\unity-custom\\nunit.framework.dll"}}, {"pid": 12345, "tid": 2, "ts": **********464949, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Assertions\\AllocatingGCMemoryConstraint.cs"}}, {"pid": 12345, "tid": 2, "ts": **********466244, "dur": 931, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Commands\\OuterUnityTestActionCommand.cs"}}, {"pid": 12345, "tid": 2, "ts": **********467460, "dur": 90, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Filters\\FullNameFilter.cs"}}, {"pid": 12345, "tid": 2, "ts": **********467590, "dur": 66, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\IStateSerializer.cs"}}, {"pid": 12345, "tid": 2, "ts": **********467658, "dur": 79, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\ITestSuiteModifier.cs"}}, {"pid": 12345, "tid": 2, "ts": **********467738, "dur": 84, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\OrderedTestSuiteModifier.cs"}}, {"pid": 12345, "tid": 2, "ts": **********467907, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Runner\\DefaultTestWorkItem.cs"}}, {"pid": 12345, "tid": 2, "ts": **********467973, "dur": 94, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Runner\\FailCommand.cs"}}, {"pid": 12345, "tid": 2, "ts": **********468068, "dur": 152, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Runner\\IEnumerableTestMethodCommand.cs"}}, {"pid": 12345, "tid": 2, "ts": **********469634, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Utils\\AssemblyProvider\\IScriptingRuntimeProxy.cs"}}, {"pid": 12345, "tid": 2, "ts": **********458395, "dur": 12273, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": **********470669, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********470835, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": **********472142, "dur": 528, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program Files (x86)\\Unity\\Unity 3D\\2022.3.33f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.dll"}}, {"pid": 12345, "tid": 2, "ts": **********475569, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program Files (x86)\\Unity\\Unity 3D\\2022.3.33f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.ComponentModel.Composition.dll"}}, {"pid": 12345, "tid": 2, "ts": **********479965, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEditor.TestRunner\\TestRun\\Tasks\\TestTaskBase.cs"}}, {"pid": 12345, "tid": 2, "ts": **********480104, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEditor.TestRunner\\TestRun\\TestJobRunner.cs"}}, {"pid": 12345, "tid": 2, "ts": **********480702, "dur": 81, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEditor.TestRunner\\TestRunner\\Messages\\RecompileScripts.cs"}}, {"pid": 12345, "tid": 2, "ts": **********480858, "dur": 73, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEditor.TestRunner\\TestRunner\\Utils\\EditorAssembliesProxy.cs"}}, {"pid": 12345, "tid": 2, "ts": **********481003, "dur": 79, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEditor.TestRunner\\TestRunner\\Utils\\EditorLoadedTestAssemblyProvider.cs"}}, {"pid": 12345, "tid": 2, "ts": **********470953, "dur": 11152, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": **********482106, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********482266, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": **********482389, "dur": 1684, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": **********484074, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********484237, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": **********484347, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": **********488000, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.8\\Runtime\\VisualScripting.Core\\Dependencies\\FullSerializer\\fsPropertyAttribute.cs"}}, {"pid": 12345, "tid": 2, "ts": **********488055, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.8\\Runtime\\VisualScripting.Core\\Dependencies\\FullSerializer\\fsResult.cs"}}, {"pid": 12345, "tid": 2, "ts": **********488150, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.8\\Runtime\\VisualScripting.Core\\Dependencies\\FullSerializer\\Internal\\fsCyclicReferenceManager.cs"}}, {"pid": 12345, "tid": 2, "ts": **********488253, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.8\\Runtime\\VisualScripting.Core\\Dependencies\\FullSerializer\\Internal\\fsPortableReflection.cs"}}, {"pid": 12345, "tid": 2, "ts": **********488394, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.8\\Runtime\\VisualScripting.Core\\Dependencies\\FullSerializer\\Internal\\fsVersionManager.cs"}}, {"pid": 12345, "tid": 2, "ts": **********488530, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.8\\Runtime\\VisualScripting.Core\\Dependencies\\FullSerializer\\Reflection\\fsReflectionUtility.cs"}}, {"pid": 12345, "tid": 2, "ts": **********488892, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.8\\Runtime\\VisualScripting.Core\\EditorBinding\\IncludeInSettingsAttribute.cs"}}, {"pid": 12345, "tid": 2, "ts": **********488944, "dur": 103, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.8\\Runtime\\VisualScripting.Core\\EditorBinding\\InspectableAttribute.cs"}}, {"pid": 12345, "tid": 2, "ts": **********489262, "dur": 70, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.8\\Runtime\\VisualScripting.Core\\EditorBinding\\Inspector\\InspectorLabelAttribute.cs"}}, {"pid": 12345, "tid": 2, "ts": **********489334, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.8\\Runtime\\VisualScripting.Core\\EditorBinding\\Inspector\\InspectorRangeAttribute.cs"}}, {"pid": 12345, "tid": 2, "ts": **********492687, "dur": 487, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.8\\Runtime\\VisualScripting.Core\\Listeners\\MonoBehaviour\\UnityOnMouseDownMessageListener.cs"}}, {"pid": 12345, "tid": 2, "ts": **********493175, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.8\\Runtime\\VisualScripting.Core\\Listeners\\MonoBehaviour\\UnityOnMouseDragMessageListener.cs"}}, {"pid": 12345, "tid": 2, "ts": **********493586, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.8\\Runtime\\VisualScripting.Core\\Listeners\\MonoBehaviour\\UnityOnTriggerEnterMessageListener.cs"}}, {"pid": 12345, "tid": 2, "ts": **********502575, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.8\\Runtime\\VisualScripting.Core\\Reflection\\IAttributeProvider.cs"}}, {"pid": 12345, "tid": 2, "ts": **********503646, "dur": 139, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.8\\Runtime\\VisualScripting.Core\\Reflection\\Namespace.cs"}}, {"pid": 12345, "tid": 2, "ts": **********504868, "dur": 2590, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.8\\Runtime\\VisualScripting.Core\\Reflection\\Operators\\SubtractionHandler.cs"}}, {"pid": 12345, "tid": 2, "ts": **********507460, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.8\\Runtime\\VisualScripting.Core\\Reflection\\Operators\\UnaryOperator.cs"}}, {"pid": 12345, "tid": 2, "ts": **********507513, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.8\\Runtime\\VisualScripting.Core\\Reflection\\Operators\\UnaryOperatorHandler.cs"}}, {"pid": 12345, "tid": 2, "ts": **********507730, "dur": 610, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.8\\Runtime\\VisualScripting.Core\\Reflection\\Optimization\\InstanceActionInvokerBase.cs"}}, {"pid": 12345, "tid": 2, "ts": **********509036, "dur": 134, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.8\\Runtime\\VisualScripting.Core\\Reflection\\Optimization\\IOptimizedAccessor.cs"}}, {"pid": 12345, "tid": 2, "ts": **********510298, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.8\\Runtime\\VisualScripting.Core\\Reflection\\TypeQualifier.cs"}}, {"pid": 12345, "tid": 2, "ts": **********510401, "dur": 71, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.8\\Runtime\\VisualScripting.Core\\Reflection\\TypeUtility.cs"}}, {"pid": 12345, "tid": 2, "ts": **********510774, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.8\\Runtime\\VisualScripting.Core\\Serialization\\ISerializationDepender.cs"}}, {"pid": 12345, "tid": 2, "ts": **********511362, "dur": 4248, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.8\\Runtime\\VisualScripting.Core\\Unity\\LudiqBehaviour.cs"}}, {"pid": 12345, "tid": 2, "ts": **********515613, "dur": 2557, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.8\\Runtime\\VisualScripting.Core\\Unity\\LudiqScriptableObject.cs"}}, {"pid": 12345, "tid": 2, "ts": **********518368, "dur": 1249, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.8\\Runtime\\VisualScripting.Core\\Unity\\UnityObjectOwnershipUtility.cs"}}, {"pid": 12345, "tid": 2, "ts": **********519785, "dur": 491, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.8\\Runtime\\VisualScripting.Core\\Utilities\\Empty.cs"}}, {"pid": 12345, "tid": 2, "ts": **********520278, "dur": 1495, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.8\\Runtime\\VisualScripting.Core\\Utilities\\EnumUtility.cs"}}, {"pid": 12345, "tid": 2, "ts": **********521962, "dur": 1015, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.8\\Runtime\\VisualScripting.Core\\Utilities\\IInitializable.cs"}}, {"pid": 12345, "tid": 2, "ts": **********523103, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.8\\Runtime\\VisualScripting.Core\\Utilities\\ReferenceCollector.cs"}}, {"pid": 12345, "tid": 2, "ts": **********523205, "dur": 1255, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.8\\Runtime\\VisualScripting.Core\\Utilities\\RuntimeVSUsageUtility.cs"}}, {"pid": 12345, "tid": 2, "ts": **********524711, "dur": 490, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.8\\Runtime\\VisualScripting.Core\\Variables\\InspectorVariableNameAttribute.cs"}}, {"pid": 12345, "tid": 2, "ts": **********525363, "dur": 613, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.8\\Runtime\\VisualScripting.Core\\Variables\\VariableDeclarationCollection.cs"}}, {"pid": 12345, "tid": 2, "ts": **********526027, "dur": 357, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.8\\Runtime\\VisualScripting.Core\\Variables\\VariableDeclarationsCloner.cs"}}, {"pid": 12345, "tid": 2, "ts": **********526390, "dur": 74, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.8\\Runtime\\VisualScripting.Core\\Variables\\VariableKind.cs"}}, {"pid": 12345, "tid": 2, "ts": **********526465, "dur": 161, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.8\\Runtime\\VisualScripting.Core\\Variables\\VariableKindAttribute.cs"}}, {"pid": 12345, "tid": 2, "ts": **********526628, "dur": 341, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.8\\Runtime\\VisualScripting.Core\\Variables\\Variables.cs"}}, {"pid": 12345, "tid": 2, "ts": **********484461, "dur": 42603, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": **********527065, "dur": 227, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********527358, "dur": 975, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********528348, "dur": 101, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": **********528450, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********528923, "dur": 866, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.8\\Runtime\\VisualScripting.Flow\\Connections\\ControlConnection.cs"}}, {"pid": 12345, "tid": 2, "ts": **********529791, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.8\\Runtime\\VisualScripting.Flow\\Connections\\InvalidConnection.cs"}}, {"pid": 12345, "tid": 2, "ts": **********529965, "dur": 550, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.8\\Runtime\\VisualScripting.Flow\\Connections\\UnitConnection.cs"}}, {"pid": 12345, "tid": 2, "ts": **********530727, "dur": 538, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.8\\Runtime\\VisualScripting.Flow\\Dependencies\\NCalc\\EvaluateParameterHandler.cs"}}, {"pid": 12345, "tid": 2, "ts": **********531582, "dur": 334, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.8\\Runtime\\VisualScripting.Flow\\Dependencies\\NCalc\\LogicalExpressionVisitor.cs"}}, {"pid": 12345, "tid": 2, "ts": **********532111, "dur": 422, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.8\\Runtime\\VisualScripting.Flow\\Dependencies\\NCalc\\UnaryExpression.cs"}}, {"pid": 12345, "tid": 2, "ts": **********532573, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.8\\Runtime\\VisualScripting.Flow\\EditorBinding\\PortKeyAttribute.cs"}}, {"pid": 12345, "tid": 2, "ts": **********532703, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.8\\Runtime\\VisualScripting.Flow\\EditorBinding\\SpecialUnitAttribute.cs"}}, {"pid": 12345, "tid": 2, "ts": **********532761, "dur": 382, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.8\\Runtime\\VisualScripting.Flow\\EditorBinding\\UnitFooterPortsAttribute.cs"}}, {"pid": 12345, "tid": 2, "ts": **********533357, "dur": 323, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.8\\Runtime\\VisualScripting.Flow\\EditorBinding\\UnitTitleAttribute.cs"}}, {"pid": 12345, "tid": 2, "ts": **********533945, "dur": 91, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.8\\Runtime\\VisualScripting.Flow\\Framework\\Codebase\\InvokeMember.cs"}}, {"pid": 12345, "tid": 2, "ts": **********534037, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.8\\Runtime\\VisualScripting.Flow\\Framework\\Codebase\\MemberUnit.cs"}}, {"pid": 12345, "tid": 2, "ts": **********534273, "dur": 94, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.8\\Runtime\\VisualScripting.Flow\\Framework\\Collections\\Dictionaries\\CreateDictionary.cs"}}, {"pid": 12345, "tid": 2, "ts": **********534886, "dur": 332, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.8\\Runtime\\VisualScripting.Flow\\Framework\\Collections\\Lists\\MergeLists.cs"}}, {"pid": 12345, "tid": 2, "ts": **********535495, "dur": 259, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.8\\Runtime\\VisualScripting.Flow\\Framework\\Control\\IBranchUnit.cs"}}, {"pid": 12345, "tid": 2, "ts": **********536657, "dur": 299, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.8\\Runtime\\VisualScripting.Flow\\Framework\\Control\\TryCatch.cs"}}, {"pid": 12345, "tid": 2, "ts": **********536958, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.8\\Runtime\\VisualScripting.Flow\\Framework\\Control\\While.cs"}}, {"pid": 12345, "tid": 2, "ts": **********537230, "dur": 274, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.8\\Runtime\\VisualScripting.Flow\\Framework\\Events\\Application\\OnApplicationLostFocus.cs"}}, {"pid": 12345, "tid": 2, "ts": **********537506, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.8\\Runtime\\VisualScripting.Flow\\Framework\\Events\\Application\\OnApplicationPause.cs"}}, {"pid": 12345, "tid": 2, "ts": **********537726, "dur": 277, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.8\\Runtime\\VisualScripting.Flow\\Framework\\Events\\CustomEventArgs.cs"}}, {"pid": 12345, "tid": 2, "ts": **********538271, "dur": 245, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.8\\Runtime\\VisualScripting.Flow\\Framework\\Events\\GUI\\OnButtonClick.cs"}}, {"pid": 12345, "tid": 2, "ts": **********538558, "dur": 1304, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.8\\Runtime\\VisualScripting.Flow\\Framework\\Events\\GUI\\OnDeselect.cs"}}, {"pid": 12345, "tid": 2, "ts": **********540030, "dur": 303, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.8\\Runtime\\VisualScripting.Flow\\Framework\\Events\\GUI\\OnGUI.cs"}}, {"pid": 12345, "tid": 2, "ts": **********540338, "dur": 618, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.8\\Runtime\\VisualScripting.Flow\\Framework\\Events\\GUI\\OnInputFieldEndEdit.cs"}}, {"pid": 12345, "tid": 2, "ts": **********540958, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.8\\Runtime\\VisualScripting.Flow\\Framework\\Events\\GUI\\OnInputFieldValueChanged.cs"}}, {"pid": 12345, "tid": 2, "ts": **********541053, "dur": 88, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.8\\Runtime\\VisualScripting.Flow\\Framework\\Events\\GUI\\OnPointerClick.cs"}}, {"pid": 12345, "tid": 2, "ts": **********541192, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.8\\Runtime\\VisualScripting.Flow\\Framework\\Events\\GUI\\OnPointerEnter.cs"}}, {"pid": 12345, "tid": 2, "ts": **********542231, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.8\\Runtime\\VisualScripting.Flow\\Framework\\Events\\Lifecycle\\LateUpdate.cs"}}, {"pid": 12345, "tid": 2, "ts": **********543212, "dur": 123, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.8\\Runtime\\VisualScripting.Flow\\Framework\\Events\\Physics2D\\OnTriggerExit2D.cs"}}, {"pid": 12345, "tid": 2, "ts": **********543337, "dur": 116, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.8\\Runtime\\VisualScripting.Flow\\Framework\\Events\\Physics2D\\OnTriggerStay2D.cs"}}, {"pid": 12345, "tid": 2, "ts": **********543454, "dur": 283, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.8\\Runtime\\VisualScripting.Flow\\Framework\\Events\\Physics2D\\TriggerEvent2DUnit.cs"}}, {"pid": 12345, "tid": 2, "ts": **********544098, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.8\\Runtime\\VisualScripting.Flow\\Framework\\Graph\\HasGraph.cs"}}, {"pid": 12345, "tid": 2, "ts": **********547378, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.8\\Runtime\\VisualScripting.Flow\\Framework\\Math\\Vector3\\Vector3Distance.cs"}}, {"pid": 12345, "tid": 2, "ts": **********548242, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.8\\Runtime\\VisualScripting.Flow\\Framework\\Math\\Vector4\\Vector4Minimum.cs"}}, {"pid": 12345, "tid": 2, "ts": **********550582, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.8\\Runtime\\VisualScripting.Flow\\Ports\\ControlOutput.cs"}}, {"pid": 12345, "tid": 2, "ts": **********528514, "dur": 23417, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": **********551932, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********552112, "dur": 86, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": **********552717, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.8\\Runtime\\VisualScripting.State\\Framework\\Graph\\HasStateGraph.cs"}}, {"pid": 12345, "tid": 2, "ts": **********553236, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.8\\Runtime\\VisualScripting.State\\StateEnterReason.cs"}}, {"pid": 12345, "tid": 2, "ts": **********552254, "dur": 1532, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": **********553787, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********553939, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": **********554058, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": **********554169, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": **********554292, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": **********555205, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ide.rider@3.0.36\\Rider\\Editor\\ProjectGeneration\\IGUIDGenerator.cs"}}, {"pid": 12345, "tid": 2, "ts": **********555598, "dur": 66, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ide.rider@3.0.36\\Rider\\Editor\\RiderScriptEditorDataPersisted.cs"}}, {"pid": 12345, "tid": 2, "ts": **********555886, "dur": 69, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ide.rider@3.0.36\\Rider\\Editor\\UnitTesting\\SyncTestRunEventsHandler.cs"}}, {"pid": 12345, "tid": 2, "ts": **********554405, "dur": 1965, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": **********556371, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********556478, "dur": 651, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": **********557129, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********557235, "dur": 368, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": **********557604, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********559238, "dur": 148, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.textmeshpro@3.0.9\\Scripts\\Editor\\TMP_ResourcesLoader.cs"}}, {"pid": 12345, "tid": 2, "ts": **********559387, "dur": 101, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.textmeshpro@3.0.9\\Scripts\\Editor\\TMP_SDFShaderGUI.cs"}}, {"pid": 12345, "tid": 2, "ts": **********559490, "dur": 72, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.textmeshpro@3.0.9\\Scripts\\Editor\\TMP_SerializedPropertyHolder.cs"}}, {"pid": 12345, "tid": 2, "ts": **********559604, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.textmeshpro@3.0.9\\Scripts\\Editor\\TMP_SpriteAssetEditor.cs"}}, {"pid": 12345, "tid": 2, "ts": **********559661, "dur": 80, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.textmeshpro@3.0.9\\Scripts\\Editor\\TMP_SpriteAssetImporter.cs"}}, {"pid": 12345, "tid": 2, "ts": **********557718, "dur": 2356, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": **********560075, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********560233, "dur": 13647, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": **********573881, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********573962, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UltimateEditorEnhancer-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": **********574096, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/VTabs.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": **********574212, "dur": 405, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/VTabs.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": **********574618, "dur": 141, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********574766, "dur": 1156, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UltimateEditorEnhancer-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": **********575923, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********576064, "dur": 385, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": **********576449, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********576921, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.DocCodeExamples.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": **********577049, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.Entities.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": **********577162, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": **********577237, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********577305, "dur": 330, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.DocCodeExamples.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": **********577636, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********577750, "dur": 328, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": **********578079, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********578184, "dur": 324, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.Entities.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": **********578508, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********578931, "dur": 1373, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********580306, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": **********580433, "dur": 757, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": **********581191, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********581300, "dur": 512, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": **********581872, "dur": 710, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********582582, "dur": 654, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********583289, "dur": 685, "ph": "X", "name": "File", "args": {"detail": "D:\\Program Files (x86)\\Unity\\Unity 3D\\2022.3.33f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\api-ms-win-core-file-l2-1-0.dll"}}, {"pid": 12345, "tid": 2, "ts": **********583236, "dur": 1072, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********584309, "dur": 114, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": **********585130, "dur": 115, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********585246, "dur": 382, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor-firstpass.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": **********585628, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********585748, "dur": 597165, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756660800182915, "dur": 1586, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEngine.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1756660800184503, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756660800185943, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program Files (x86)\\Unity\\Unity 3D\\2022.3.33f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.Extensions.dll"}}, {"pid": 12345, "tid": 2, "ts": 1756660800184628, "dur": 2014, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1756660800186643, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756660800186755, "dur": 1638, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1756660800188394, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756660800188996, "dur": 190, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program Files (x86)\\Unity\\Unity 3D\\2022.3.33f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Threading.Thread.dll"}}, {"pid": 12345, "tid": 2, "ts": 1756660800189195, "dur": 66, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program Files (x86)\\Unity\\Unity 3D\\2022.3.33f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Xml.XDocument.dll"}}, {"pid": 12345, "tid": 2, "ts": 1756660800188502, "dur": 1927, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1756660800190430, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756660800190538, "dur": 1627, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1756660800192165, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756660800192271, "dur": 1615, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1756660800193887, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756660800194002, "dur": 1601, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PlasticSCM.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1756660800195603, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756660800195696, "dur": 1624, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PlasticSCM.Editor.Entities.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1756660800197325, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756660800197425, "dur": 1621, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1756660800199047, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756660800199161, "dur": 1614, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEditor.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1756660800200776, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756660800201951, "dur": 95, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program Files (x86)\\Unity\\Unity 3D\\2022.3.33f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.TextCoreTextEngineModule.dll"}}, {"pid": 12345, "tid": 2, "ts": 1756660800200855, "dur": 2000, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Shared.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1756660800202857, "dur": 145, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756660800203010, "dur": 1660, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Utilities.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1756660800204671, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756660800204787, "dur": 1871, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1756660800206659, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756660800206914, "dur": 153, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756660800208418, "dur": 635084, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756660800843504, "dur": 261, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 2, "ts": 1756660800843503, "dur": 263, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 2, "ts": 1756660800843827, "dur": 3270, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 2, "ts": 1756660800847099, "dur": 403109, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********432450, "dur": 21283, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********453748, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program Files (x86)\\Unity\\Unity 3D\\2022.3.33f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityAnalyticsModule.dll"}}, {"pid": 12345, "tid": 3, "ts": **********453736, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_EA8E1315F893D5F7.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": **********453955, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_45621F704B93F6D1.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": **********454441, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********454512, "dur": 64, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program Files (x86)\\Unity\\Unity 3D\\2022.3.33f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.SpriteShapeModule.dll"}}, {"pid": 12345, "tid": 3, "ts": **********454510, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_8FDE3E5FB344B13A.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": **********454578, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********454638, "dur": 3524, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program Files (x86)\\Unity\\Unity 3D\\2022.3.33f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.SharedInternalsModule.dll"}}, {"pid": 12345, "tid": 3, "ts": **********454637, "dur": 3528, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_3B036F4287C9841F.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": **********458526, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": **********459172, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program Files (x86)\\Unity\\Unity 3D\\2022.3.33f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\Microsoft.Win32.Primitives.dll"}}, {"pid": 12345, "tid": 3, "ts": **********459228, "dur": 1429, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program Files (x86)\\Unity\\Unity 3D\\2022.3.33f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.AppContext.dll"}}, {"pid": 12345, "tid": 3, "ts": **********462403, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program Files (x86)\\Unity\\Unity 3D\\2022.3.33f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.Security.dll"}}, {"pid": 12345, "tid": 3, "ts": **********462459, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program Files (x86)\\Unity\\Unity 3D\\2022.3.33f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.Sockets.dll"}}, {"pid": 12345, "tid": 3, "ts": **********462519, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program Files (x86)\\Unity\\Unity 3D\\2022.3.33f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.WebHeaderCollection.dll"}}, {"pid": 12345, "tid": 3, "ts": **********462577, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program Files (x86)\\Unity\\Unity 3D\\2022.3.33f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.WebSockets.Client.dll"}}, {"pid": 12345, "tid": 3, "ts": **********462682, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program Files (x86)\\Unity\\Unity 3D\\2022.3.33f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Numerics.Vectors.dll"}}, {"pid": 12345, "tid": 3, "ts": **********462736, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program Files (x86)\\Unity\\Unity 3D\\2022.3.33f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ObjectModel.dll"}}, {"pid": 12345, "tid": 3, "ts": **********462793, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program Files (x86)\\Unity\\Unity 3D\\2022.3.33f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.DispatchProxy.dll"}}, {"pid": 12345, "tid": 3, "ts": **********462897, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program Files (x86)\\Unity\\Unity 3D\\2022.3.33f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.Emit.dll"}}, {"pid": 12345, "tid": 3, "ts": **********463235, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program Files (x86)\\Unity\\Unity 3D\\2022.3.33f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.CompilerServices.VisualC.dll"}}, {"pid": 12345, "tid": 3, "ts": **********463819, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program Files (x86)\\Unity\\Unity 3D\\2022.3.33f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.Cryptography.Primitives.dll"}}, {"pid": 12345, "tid": 3, "ts": **********466240, "dur": 231, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\FontData.cs"}}, {"pid": 12345, "tid": 3, "ts": **********466763, "dur": 64, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\IMaskable.cs"}}, {"pid": 12345, "tid": 3, "ts": **********466873, "dur": 69, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Layout\\AspectRatioFitter.cs"}}, {"pid": 12345, "tid": 3, "ts": **********466943, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Layout\\CanvasScaler.cs"}}, {"pid": 12345, "tid": 3, "ts": **********467454, "dur": 79, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\MaskUtilities.cs"}}, {"pid": 12345, "tid": 3, "ts": **********467612, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\MultipleDisplayUtilities.cs"}}, {"pid": 12345, "tid": 3, "ts": **********467687, "dur": 64, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Navigation.cs"}}, {"pid": 12345, "tid": 3, "ts": **********467752, "dur": 463, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\RawImage.cs"}}, {"pid": 12345, "tid": 3, "ts": **********458643, "dur": 10332, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": **********468976, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********469148, "dur": 714, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********469863, "dur": 746, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********470610, "dur": 704, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********471314, "dur": 697, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********472011, "dur": 814, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********472826, "dur": 686, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********473512, "dur": 689, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********474202, "dur": 682, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********474884, "dur": 735, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********475620, "dur": 666, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********476286, "dur": 657, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********476943, "dur": 671, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********477615, "dur": 673, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********478288, "dur": 666, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********479412, "dur": 9958, "ph": "X", "name": "File", "args": {"detail": "D:\\Program Files (x86)\\Unity\\Unity 3D\\2022.3.33f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Security.Cryptography.Encoding.dll"}}, {"pid": 12345, "tid": 3, "ts": **********478954, "dur": 10610, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********489565, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": **********489691, "dur": 721, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": **********490413, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********490531, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": **********490644, "dur": 564, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": **********491209, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********491409, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": **********491527, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": **********491643, "dur": 1027, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": **********492671, "dur": 573, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********493581, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@14.0.11\\ShaderLibrary\\Debug\\DebugViewEnums.cs"}}, {"pid": 12345, "tid": 3, "ts": **********493253, "dur": 461, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": **********493715, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********493867, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": **********495550, "dur": 169, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@14.0.11\\Runtime\\2D\\UTess2D\\UTess.cs"}}, {"pid": 12345, "tid": 3, "ts": **********495721, "dur": 108, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@14.0.11\\Runtime\\AssemblyInfo.cs"}}, {"pid": 12345, "tid": 3, "ts": **********495831, "dur": 542, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@14.0.11\\Runtime\\ComponentUtility.cs"}}, {"pid": 12345, "tid": 3, "ts": **********496526, "dur": 149, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@14.0.11\\Runtime\\Data\\UniversalRenderPipelineEditorResources.cs"}}, {"pid": 12345, "tid": 3, "ts": **********496676, "dur": 156, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@14.0.11\\Runtime\\Data\\XRSystemData.cs"}}, {"pid": 12345, "tid": 3, "ts": **********496944, "dur": 89, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@14.0.11\\Runtime\\Debug\\DebugDisplaySettingsRendering.cs"}}, {"pid": 12345, "tid": 3, "ts": **********497144, "dur": 67, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@14.0.11\\Runtime\\Debug\\UniversalRenderPipelineDebugDisplaySettings.cs"}}, {"pid": 12345, "tid": 3, "ts": **********497286, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@14.0.11\\Runtime\\Decal\\DBuffer\\DecalForwardEmissivePass.cs"}}, {"pid": 12345, "tid": 3, "ts": **********497338, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@14.0.11\\Runtime\\Decal\\DecalDrawErrorRenderPass.cs"}}, {"pid": 12345, "tid": 3, "ts": **********497508, "dur": 174, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@14.0.11\\Runtime\\Decal\\Entities\\DecalChunk.cs"}}, {"pid": 12345, "tid": 3, "ts": **********493990, "dur": 7585, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": **********501576, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********501841, "dur": 83, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": **********528920, "dur": 2317, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program Files (x86)\\Unity\\Unity 3D\\2022.3.33f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.Transactions.dll"}}, {"pid": 12345, "tid": 3, "ts": **********531259, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.9.2\\Editor\\ApplicationDataPath.cs"}}, {"pid": 12345, "tid": 3, "ts": **********531578, "dur": 328, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.9.2\\Editor\\AssetMenu\\Dialogs\\CheckinDialogOperations.cs"}}, {"pid": 12345, "tid": 3, "ts": **********532151, "dur": 432, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.9.2\\Editor\\AssetOverlays\\Cache\\AssetStatusCache.cs"}}, {"pid": 12345, "tid": 3, "ts": **********532707, "dur": 392, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.9.2\\Editor\\AssetOverlays\\Cache\\RemoteStatusCache.cs"}}, {"pid": 12345, "tid": 3, "ts": **********533227, "dur": 64, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.9.2\\Editor\\AssetsUtils\\AssetsPath.cs"}}, {"pid": 12345, "tid": 3, "ts": **********533328, "dur": 363, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.9.2\\Editor\\AssetsUtils\\LoadAsset.cs"}}, {"pid": 12345, "tid": 3, "ts": **********533946, "dur": 91, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.9.2\\Editor\\AssetsUtils\\ProjectPath.cs"}}, {"pid": 12345, "tid": 3, "ts": **********534038, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.9.2\\Editor\\AssetsUtils\\RefreshAsset.cs"}}, {"pid": 12345, "tid": 3, "ts": **********534224, "dur": 114, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.9.2\\Editor\\BoolSetting.cs"}}, {"pid": 12345, "tid": 3, "ts": **********534738, "dur": 485, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.9.2\\Editor\\CloudDrive\\GetProposedOrganizationProject.cs"}}, {"pid": 12345, "tid": 3, "ts": **********535465, "dur": 288, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.9.2\\Editor\\CloudDrive\\Workspaces\\DirectoryContent\\DrawItemNameBar.cs"}}, {"pid": 12345, "tid": 3, "ts": **********535755, "dur": 87, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.9.2\\Editor\\CloudDrive\\Workspaces\\DirectoryContent\\DrawItemsGridView.cs"}}, {"pid": 12345, "tid": 3, "ts": **********536107, "dur": 258, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.9.2\\Editor\\CloudDrive\\Workspaces\\Tree\\CloudWorkspacesSelection.cs"}}, {"pid": 12345, "tid": 3, "ts": **********536375, "dur": 149, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.9.2\\Editor\\CloudDrive\\Workspaces\\Tree\\CloudWorkspacesTreeView.cs"}}, {"pid": 12345, "tid": 3, "ts": **********536525, "dur": 466, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.9.2\\Editor\\CloudDrive\\Workspaces\\Tree\\CloudWorkspacesTreeViewItem.cs"}}, {"pid": 12345, "tid": 3, "ts": **********537190, "dur": 274, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.9.2\\Editor\\Configuration\\CloudEdition\\Welcome\\OrganizationPanel.cs"}}, {"pid": 12345, "tid": 3, "ts": **********537505, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.9.2\\Editor\\Configuration\\CloudEdition\\Welcome\\SignInWithEmailPanel.cs"}}, {"pid": 12345, "tid": 3, "ts": **********537723, "dur": 255, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.9.2\\Editor\\Configuration\\EncryptionConfigurationDialog.cs"}}, {"pid": 12345, "tid": 3, "ts": **********537980, "dur": 71, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.9.2\\Editor\\Configuration\\MissingEncryptionPasswordPromptHandler.cs"}}, {"pid": 12345, "tid": 3, "ts": **********538211, "dur": 277, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.9.2\\Editor\\Developer\\CheckinProgress.cs"}}, {"pid": 12345, "tid": 3, "ts": **********538785, "dur": 400, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.9.2\\Editor\\Developer\\UpdateReport\\UpdateReportListView.cs"}}, {"pid": 12345, "tid": 3, "ts": **********539227, "dur": 124, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.9.2\\Editor\\EnumExtensions.cs"}}, {"pid": 12345, "tid": 3, "ts": **********539496, "dur": 253, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.9.2\\Editor\\Gluon\\IncomingChangesNotification.cs"}}, {"pid": 12345, "tid": 3, "ts": **********540067, "dur": 235, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.9.2\\Editor\\Hub\\CommandLineArguments.cs"}}, {"pid": 12345, "tid": 3, "ts": **********540589, "dur": 259, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.9.2\\Editor\\IntSetting.cs"}}, {"pid": 12345, "tid": 3, "ts": **********541205, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.9.2\\Editor\\QueryVisualElementsExtensions.cs"}}, {"pid": 12345, "tid": 3, "ts": **********543239, "dur": 539, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.9.2\\Editor\\UI\\Errors\\ErrorsPanel.cs"}}, {"pid": 12345, "tid": 3, "ts": **********543779, "dur": 90, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.9.2\\Editor\\UI\\FindEditorWindow.cs"}}, {"pid": 12345, "tid": 3, "ts": **********544103, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.9.2\\Editor\\UI\\MeasureMaxWidth.cs"}}, {"pid": 12345, "tid": 3, "ts": **********548246, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.9.2\\Editor\\Views\\DateFilter.cs"}}, {"pid": 12345, "tid": 3, "ts": **********552719, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.9.2\\Editor\\WebApi\\ErrorResponse.cs"}}, {"pid": 12345, "tid": 3, "ts": **********552944, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.9.2\\Editor\\_Deprecated\\CollabMigration\\MigrateCollabProject.cs"}}, {"pid": 12345, "tid": 3, "ts": **********501962, "dur": 51319, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": **********553282, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********553400, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UltimateEditorEnhancer.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": **********553517, "dur": 339, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UltimateEditorEnhancer.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": **********553856, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********553973, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": **********554099, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": **********554220, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": **********554341, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": **********555207, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ide.visualstudio@2.0.23\\Editor\\Messaging\\Messenger.cs"}}, {"pid": 12345, "tid": 3, "ts": **********555562, "dur": 433, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ide.visualstudio@2.0.23\\Editor\\ProjectGeneration\\GUIDProvider.cs"}}, {"pid": 12345, "tid": 3, "ts": **********554457, "dur": 2473, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": **********556931, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********557057, "dur": 373, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": **********557431, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********557542, "dur": 355, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": **********557898, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********559236, "dur": 163, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@1.7.7\\Editor\\Animation\\BindingSelector.cs"}}, {"pid": 12345, "tid": 3, "ts": **********559400, "dur": 87, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@1.7.7\\Editor\\Animation\\BindingTreeViewDataSource.cs"}}, {"pid": 12345, "tid": 3, "ts": **********559488, "dur": 517, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@1.7.7\\Editor\\Animation\\BindingTreeViewDataSourceGUI.cs"}}, {"pid": 12345, "tid": 3, "ts": **********562478, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@1.7.7\\Editor\\Manipulators\\Move\\MovingItems.cs"}}, {"pid": 12345, "tid": 3, "ts": **********563597, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@1.7.7\\Editor\\Recording\\TimelineRecording_PlayableAsset.cs"}}, {"pid": 12345, "tid": 3, "ts": **********563658, "dur": 64, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@1.7.7\\Editor\\Recording\\TrackAssetRecordingExtensions.cs"}}, {"pid": 12345, "tid": 3, "ts": **********563805, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@1.7.7\\Editor\\Signals\\SignalEmitterEditor.cs"}}, {"pid": 12345, "tid": 3, "ts": **********564136, "dur": 193, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@1.7.7\\Editor\\Signals\\TreeView\\SignalReceiverItem.cs"}}, {"pid": 12345, "tid": 3, "ts": **********564982, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@1.7.7\\Editor\\treeview\\Drawers\\Layers\\ItemsLayer.cs"}}, {"pid": 12345, "tid": 3, "ts": **********565038, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@1.7.7\\Editor\\treeview\\Drawers\\Layers\\MarkersLayer.cs"}}, {"pid": 12345, "tid": 3, "ts": **********565097, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@1.7.7\\Editor\\treeview\\Drawers\\TrackDrawer.cs"}}, {"pid": 12345, "tid": 3, "ts": **********565301, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@1.7.7\\Editor\\treeview\\ItemGui\\TimelineClipGUI.cs"}}, {"pid": 12345, "tid": 3, "ts": **********565562, "dur": 356, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@1.7.7\\Editor\\treeview\\Manipulator.cs"}}, {"pid": 12345, "tid": 3, "ts": **********566164, "dur": 386, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@1.7.7\\Editor\\treeview\\TimelineDragging.cs"}}, {"pid": 12345, "tid": 3, "ts": **********566792, "dur": 331, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@1.7.7\\Editor\\treeview\\TrackGui\\TrackResizeHandle.cs"}}, {"pid": 12345, "tid": 3, "ts": **********567330, "dur": 345, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@1.7.7\\Editor\\Utilities\\AnimatedParameterUtility.cs"}}, {"pid": 12345, "tid": 3, "ts": **********567957, "dur": 319, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@1.7.7\\Editor\\Utilities\\FileUtility.cs"}}, {"pid": 12345, "tid": 3, "ts": **********568515, "dur": 333, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@1.7.7\\Editor\\Utilities\\PropertyCollector.cs"}}, {"pid": 12345, "tid": 3, "ts": **********568890, "dur": 704, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@1.7.7\\Editor\\Utilities\\Scopes\\GUIColorOverride.cs"}}, {"pid": 12345, "tid": 3, "ts": **********569706, "dur": 358, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@1.7.7\\Editor\\Utilities\\Scopes\\HorizontalScope.cs"}}, {"pid": 12345, "tid": 3, "ts": **********570323, "dur": 357, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@1.7.7\\Editor\\Utilities\\TimeFormat.cs"}}, {"pid": 12345, "tid": 3, "ts": **********570825, "dur": 585, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@1.7.7\\Editor\\Utilities\\TypeUtility.cs"}}, {"pid": 12345, "tid": 3, "ts": **********571691, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@1.7.7\\Editor\\Window\\PlaybackScroller.cs"}}, {"pid": 12345, "tid": 3, "ts": **********571885, "dur": 1557, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@1.7.7\\Editor\\Window\\TimelinePlaybackControls.cs"}}, {"pid": 12345, "tid": 3, "ts": **********573445, "dur": 69, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@1.7.7\\Editor\\Window\\TimelineWindow.cs"}}, {"pid": 12345, "tid": 3, "ts": **********573515, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@1.7.7\\Editor\\Window\\TimelineWindowAnalytics.cs"}}, {"pid": 12345, "tid": 3, "ts": **********573602, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@1.7.7\\Editor\\Window\\TimelineWindow_ActiveTimeline.cs"}}, {"pid": 12345, "tid": 3, "ts": **********573896, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@1.7.7\\Editor\\Window\\TimelineWindow_Navigator.cs"}}, {"pid": 12345, "tid": 3, "ts": **********574223, "dur": 7111, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@1.7.7\\Editor\\Window\\TimelineWindow_TrackGui.cs"}}, {"pid": 12345, "tid": 3, "ts": **********558008, "dur": 23515, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": **********581524, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********581624, "dur": 197, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": **********581874, "dur": 709, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********582583, "dur": 656, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********584035, "dur": 109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor-firstpass.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": **********584189, "dur": 105, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": **********585132, "dur": 597784, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756660800182918, "dur": 1705, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UltimateEditorEnhancer.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1756660800184624, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756660800186061, "dur": 207, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program Files (x86)\\Unity\\Unity 3D\\2022.3.33f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Data.Common.dll"}}, {"pid": 12345, "tid": 3, "ts": 1756660800184733, "dur": 2051, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Shaders.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1756660800186785, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756660800186889, "dur": 1663, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp-firstpass.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1756660800188553, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756660800188635, "dur": 2147, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1756660800190783, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756660800190884, "dur": 1600, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.DocCodeExamples.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1756660800192485, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756660800192566, "dur": 1603, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1756660800194170, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756660800194253, "dur": 1607, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.CollabProxy.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1756660800195861, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756660800195940, "dur": 1614, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1756660800197554, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756660800197637, "dur": 1640, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1756660800199278, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756660800199359, "dur": 1610, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/VTabs.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1756660800200970, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756660800201049, "dur": 1967, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1756660800203017, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756660800203114, "dur": 1613, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UltimateEditorEnhancer-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1756660800204727, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756660800204818, "dur": 1734, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1756660800206553, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756660800206644, "dur": 1529, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp-Editor-firstpass.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1756660800208174, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756660800208421, "dur": 1039110, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756660801247534, "dur": 368, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 3, "ts": 1756660801247533, "dur": 370, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 3, "ts": 1756660801247915, "dur": 2100, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 4, "ts": **********432489, "dur": 22845, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********455335, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_1E200FA4528C6C47.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": **********455682, "dur": 68, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program Files (x86)\\Unity\\Unity 3D\\2022.3.33f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.UIBuilderModule.dll"}}, {"pid": 12345, "tid": 4, "ts": **********455680, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_8DC5050FBC78DA50.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": **********455928, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********457504, "dur": 114, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program Files (x86)\\Unity\\Unity 3D\\2022.3.33f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.WindModule.dll"}}, {"pid": 12345, "tid": 4, "ts": **********457503, "dur": 117, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_EE44B8D427D1401E.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": **********459096, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 4, "ts": **********459148, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********460958, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": **********462290, "dur": 758, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": **********466251, "dur": 231, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********466755, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********467457, "dur": 241, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********467941, "dur": 773, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********468715, "dur": 674, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********469390, "dur": 788, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********470179, "dur": 723, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********470990, "dur": 706, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********471697, "dur": 660, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********472357, "dur": 687, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********473045, "dur": 679, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********473724, "dur": 681, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********474405, "dur": 697, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********475102, "dur": 744, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********475846, "dur": 672, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********476518, "dur": 683, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********477201, "dur": 691, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********477892, "dur": 670, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********478563, "dur": 667, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********479230, "dur": 672, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********479902, "dur": 702, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********480604, "dur": 627, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********481231, "dur": 688, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********481919, "dur": 633, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********482636, "dur": 672, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********483308, "dur": 693, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********484001, "dur": 700, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********484702, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": **********487996, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core@14.0.11\\Runtime\\Debugging\\ShaderDebugPrintManager.cs"}}, {"pid": 12345, "tid": 4, "ts": **********488051, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core@14.0.11\\Runtime\\Debugging\\VolumeDebugSettings.cs"}}, {"pid": 12345, "tid": 4, "ts": **********488148, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core@14.0.11\\Runtime\\Documentation.cs"}}, {"pid": 12345, "tid": 4, "ts": **********488248, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core@14.0.11\\Runtime\\Lighting\\ProbeVolume\\ProbeBrickIndex.cs"}}, {"pid": 12345, "tid": 4, "ts": **********488390, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core@14.0.11\\Runtime\\Lighting\\ProbeVolume\\ProbeReferenceVolume.cs"}}, {"pid": 12345, "tid": 4, "ts": **********484822, "dur": 6845, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": **********491668, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********491813, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": **********492714, "dur": 517, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core@14.0.11\\Editor\\Camera\\CameraUI.Drawers.cs"}}, {"pid": 12345, "tid": 4, "ts": **********493909, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core@14.0.11\\Editor\\Debugging\\DebugUIDrawer.Builtins.cs"}}, {"pid": 12345, "tid": 4, "ts": **********495585, "dur": 148, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core@14.0.11\\Editor\\Lighting\\ProbeVolume\\SerializedProbeTouchupVolume.cs"}}, {"pid": 12345, "tid": 4, "ts": **********495773, "dur": 64, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core@14.0.11\\Editor\\Lighting\\Shadow\\ShadowCascadeGUI.cs"}}, {"pid": 12345, "tid": 4, "ts": **********495996, "dur": 294, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core@14.0.11\\Editor\\LookDev\\Compositor.cs"}}, {"pid": 12345, "tid": 4, "ts": **********496971, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core@14.0.11\\Editor\\PostProcessing\\LensFlareDataSRPEditor.cs"}}, {"pid": 12345, "tid": 4, "ts": **********497352, "dur": 72, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core@14.0.11\\Editor\\RenderPipeline\\RenderPipelineGlobalSettingsUI.Drawers.cs"}}, {"pid": 12345, "tid": 4, "ts": **********497608, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core@14.0.11\\Editor\\ShaderGenerator\\CSharpToHLSL.cs"}}, {"pid": 12345, "tid": 4, "ts": **********491936, "dur": 6792, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": **********498729, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********498880, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": **********502660, "dur": 166, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.0.11\\Editor\\Data\\Graphs\\VirtualTextureInputMaterialSlot.cs"}}, {"pid": 12345, "tid": 4, "ts": **********505129, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.0.11\\Editor\\Data\\Nodes\\Artistic\\Blend\\BlendNode.cs"}}, {"pid": 12345, "tid": 4, "ts": **********505630, "dur": 121, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.0.11\\Editor\\Data\\Nodes\\BlockNode.cs"}}, {"pid": 12345, "tid": 4, "ts": **********505752, "dur": 133, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.0.11\\Editor\\Data\\Nodes\\Channel\\CombineNode.cs"}}, {"pid": 12345, "tid": 4, "ts": **********505989, "dur": 67, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.0.11\\Editor\\Data\\Nodes\\CodeFunctionNode.cs"}}, {"pid": 12345, "tid": 4, "ts": **********506478, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.0.11\\Editor\\Data\\Nodes\\Input\\Basic\\Vector3Node.cs"}}, {"pid": 12345, "tid": 4, "ts": **********507034, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.0.11\\Editor\\Data\\Nodes\\Input\\Gradient\\BlackbodyNode.cs"}}, {"pid": 12345, "tid": 4, "ts": **********507088, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.0.11\\Editor\\Data\\Nodes\\Input\\Gradient\\GradientNode.cs"}}, {"pid": 12345, "tid": 4, "ts": **********507381, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.0.11\\Editor\\Data\\Nodes\\Input\\Matrix\\Matrix3Node.cs"}}, {"pid": 12345, "tid": 4, "ts": **********507516, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.0.11\\Editor\\Data\\Nodes\\Input\\PBR\\DielectricSpecularNode.cs"}}, {"pid": 12345, "tid": 4, "ts": **********509146, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.0.11\\Editor\\Data\\Nodes\\Math\\Derivative\\DDXNode.cs"}}, {"pid": 12345, "tid": 4, "ts": **********510775, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.0.11\\Editor\\Data\\Nodes\\Math\\Vector\\TransformNode.cs"}}, {"pid": 12345, "tid": 4, "ts": **********512144, "dur": 158, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.0.11\\Editor\\Data\\Nodes\\Utility\\SplitTextureTransformNode.cs"}}, {"pid": 12345, "tid": 4, "ts": **********512303, "dur": 312, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.0.11\\Editor\\Data\\Nodes\\Utility\\SubGraphNode.cs"}}, {"pid": 12345, "tid": 4, "ts": **********522353, "dur": 207, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.0.11\\Editor\\Generation\\Targets\\Fullscreen\\FullscreenData.cs"}}, {"pid": 12345, "tid": 4, "ts": **********522645, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.0.11\\Editor\\Generation\\Targets\\Fullscreen\\FullscreenSubTarget.cs"}}, {"pid": 12345, "tid": 4, "ts": **********522701, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.0.11\\Editor\\Generation\\Targets\\Fullscreen\\Includes\\FullscreenShaderPass.cs"}}, {"pid": 12345, "tid": 4, "ts": **********523105, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.0.11\\Editor\\Interface\\IConditional.cs"}}, {"pid": 12345, "tid": 4, "ts": **********523429, "dur": 67, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.0.11\\Editor\\Serialization\\MultiJsonInternal.cs"}}, {"pid": 12345, "tid": 4, "ts": **********524114, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.0.11\\Editor\\Util\\IndexSet.cs"}}, {"pid": 12345, "tid": 4, "ts": **********499020, "dur": 25376, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": **********524397, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********524557, "dur": 97, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": **********526116, "dur": 10462, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@14.0.11\\Editor\\2D\\ShapeEditor\\EditablePath\\EditablePathController.cs"}}, {"pid": 12345, "tid": 4, "ts": **********536629, "dur": 315, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@14.0.11\\Editor\\2D\\ShapeEditor\\EditablePath\\EditablePathUtility.cs"}}, {"pid": 12345, "tid": 4, "ts": **********537188, "dur": 258, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@14.0.11\\Editor\\2D\\ShapeEditor\\EditorTool\\GenericScriptablePath.cs"}}, {"pid": 12345, "tid": 4, "ts": **********537760, "dur": 219, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@14.0.11\\Editor\\2D\\ShapeEditor\\GUIFramework\\CommandAction.cs"}}, {"pid": 12345, "tid": 4, "ts": **********538245, "dur": 277, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@14.0.11\\Editor\\2D\\ShapeEditor\\GUIFramework\\HoveredControlAction.cs"}}, {"pid": 12345, "tid": 4, "ts": **********538524, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@14.0.11\\Editor\\2D\\ShapeEditor\\GUIFramework\\IGUIState.cs"}}, {"pid": 12345, "tid": 4, "ts": **********538788, "dur": 424, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@14.0.11\\Editor\\2D\\ShapeEditor\\Selection\\PointRectSelector.cs"}}, {"pid": 12345, "tid": 4, "ts": **********539255, "dur": 67, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@14.0.11\\Editor\\2D\\ShapeEditor\\Selection\\SerializableSelection.cs"}}, {"pid": 12345, "tid": 4, "ts": **********539474, "dur": 297, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@14.0.11\\Editor\\2D\\ShapeEditor\\View\\CreatePointAction.cs"}}, {"pid": 12345, "tid": 4, "ts": **********539772, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@14.0.11\\Editor\\2D\\ShapeEditor\\View\\Drawer.cs"}}, {"pid": 12345, "tid": 4, "ts": **********540030, "dur": 244, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@14.0.11\\Editor\\AnimationClipUpgrader.cs"}}, {"pid": 12345, "tid": 4, "ts": **********540586, "dur": 260, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@14.0.11\\Editor\\AssetPostProcessors\\ShaderGraphMaterialsUpdater.cs"}}, {"pid": 12345, "tid": 4, "ts": **********541196, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@14.0.11\\Editor\\Camera\\UniversalRenderPipelineCameraUI.Output.Skin.cs"}}, {"pid": 12345, "tid": 4, "ts": **********543245, "dur": 165, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@14.0.11\\Editor\\RendererFeatures\\FullScreenPassRendererFeatureEditor.cs"}}, {"pid": 12345, "tid": 4, "ts": **********543411, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@14.0.11\\Editor\\RendererFeatures\\NewRendererFeatureDropdownItem.cs"}}, {"pid": 12345, "tid": 4, "ts": **********543522, "dur": 208, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@14.0.11\\Editor\\RendererFeatures\\ScreenSpaceAmbientOcclusionEditor.cs"}}, {"pid": 12345, "tid": 4, "ts": **********544104, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@14.0.11\\Editor\\ShaderGraph\\Includes\\DecalMeshBiasTypeEnum.cs"}}, {"pid": 12345, "tid": 4, "ts": **********524692, "dur": 21378, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": **********546072, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********546187, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": **********547375, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.8\\Editor\\VisualScripting.Core\\Assignment\\AssignsAttribute.cs"}}, {"pid": 12345, "tid": 4, "ts": **********552210, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.8\\Editor\\VisualScripting.Core\\Inspection\\Special\\ListInspector.cs"}}, {"pid": 12345, "tid": 4, "ts": **********552720, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.8\\Editor\\VisualScripting.Core\\Inspection\\Unity\\RectInspector.cs"}}, {"pid": 12345, "tid": 4, "ts": **********552777, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.8\\Editor\\VisualScripting.Core\\Inspection\\Unity\\UnityObjectInspector.cs"}}, {"pid": 12345, "tid": 4, "ts": **********552947, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.8\\Editor\\VisualScripting.Core\\Inspection\\Unity\\VectorInspector.cs"}}, {"pid": 12345, "tid": 4, "ts": **********555209, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.8\\Editor\\VisualScripting.Core\\Platforms\\EditorPlatformUtility.cs"}}, {"pid": 12345, "tid": 4, "ts": **********555469, "dur": 183, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.8\\Editor\\VisualScripting.Core\\Plugin\\Acknowledgements\\Acknowledgement_DeepCopy.cs"}}, {"pid": 12345, "tid": 4, "ts": **********559241, "dur": 145, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.8\\Editor\\VisualScripting.Core\\Products\\Product.cs"}}, {"pid": 12345, "tid": 4, "ts": **********559681, "dur": 76, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.8\\Editor\\VisualScripting.Core\\Reflection\\LooseAssemblyNameOption.cs"}}, {"pid": 12345, "tid": 4, "ts": **********559758, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.8\\Editor\\VisualScripting.Core\\Reflection\\LooseAssemblyNameOptionTree.cs"}}, {"pid": 12345, "tid": 4, "ts": **********559821, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.8\\Editor\\VisualScripting.Core\\Reflection\\MemberOption.cs"}}, {"pid": 12345, "tid": 4, "ts": **********559925, "dur": 87, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.8\\Editor\\VisualScripting.Core\\Reflection\\NamespaceOption.cs"}}, {"pid": 12345, "tid": 4, "ts": **********562476, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.8\\Editor\\VisualScripting.Core\\Widgets\\Groups\\GraphGroupEditor.cs"}}, {"pid": 12345, "tid": 4, "ts": **********563214, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.8\\Editor\\VisualScripting.Core\\Windows\\AboutWindow\\ChangelogPage.cs"}}, {"pid": 12345, "tid": 4, "ts": **********564158, "dur": 81, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.8\\Editor\\VisualScripting.Core\\Windows\\Wizard.cs"}}, {"pid": 12345, "tid": 4, "ts": **********546330, "dur": 17963, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": **********564295, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********564447, "dur": 95, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": **********565046, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.8\\Editor\\VisualScripting.Flow\\BoltFlowNameUtility.cs"}}, {"pid": 12345, "tid": 4, "ts": **********565100, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.8\\Editor\\VisualScripting.Flow\\Connections\\ControlConnectionWidget.cs"}}, {"pid": 12345, "tid": 4, "ts": **********568234, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.8\\Editor\\VisualScripting.Flow\\Framework\\Variables\\Obsolete\\SetVariableUnitOption.cs"}}, {"pid": 12345, "tid": 4, "ts": **********568589, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.8\\Editor\\VisualScripting.Flow\\Invocations\\InvocationInspector.cs"}}, {"pid": 12345, "tid": 4, "ts": **********564583, "dur": 6544, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": **********571128, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********571242, "dur": 8578, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********579831, "dur": 88, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": **********579920, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********580214, "dur": 207, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program Files (x86)\\Unity\\Unity 3D\\2022.3.33f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.IsolatedStorage.dll"}}, {"pid": 12345, "tid": 4, "ts": **********580602, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.8\\Editor\\VisualScripting.State\\Analytics\\StateMacroSavedEvent.cs"}}, {"pid": 12345, "tid": 4, "ts": **********581223, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.8\\Editor\\VisualScripting.State\\Plugin\\Changelogs\\Changelog_1_0_2.cs"}}, {"pid": 12345, "tid": 4, "ts": **********579997, "dur": 3030, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": **********583028, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********583172, "dur": 98, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": **********583312, "dur": 378, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": **********583690, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********583821, "dur": 207, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********584033, "dur": 104, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-firstpass.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": **********584179, "dur": 402, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-firstpass.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": **********584582, "dur": 650, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********585603, "dur": 233, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.rsp"}}, {"pid": 12345, "tid": 4, "ts": **********585242, "dur": 595, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": **********586313, "dur": 63, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********586391, "dur": 735498, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1756660800332071, "dur": 190, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.ref.dll"}}, {"pid": 12345, "tid": 4, "ts": 1756660800331683, "dur": 636, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1756660800332749, "dur": 65, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756660800332832, "dur": 499970, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1756660800841496, "dur": 370, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 4, "ts": 1756660800843258, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.burst@1.8.15\\Unity.Burst.Unsafe.dll"}}, {"pid": 12345, "tid": 4, "ts": 1756660800841495, "dur": 3060, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1756660800846360, "dur": 428, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756660800846804, "dur": 363974, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1756660801247528, "dur": 240, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 4, "ts": 1756660801247527, "dur": 242, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 4, "ts": 1756660801247791, "dur": 2077, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 4, "ts": 1756660801249873, "dur": 238, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1756660801265978, "dur": 3591, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 35942, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "netcorerun.dll"}}, {"pid": 35942, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-1"}}, {"pid": 35942, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 35942, "tid": 1, "ts": 1756660798490977, "dur": 671896, "ph": "X", "name": "BuildProgram", "args": {}}, {"pid": 35942, "tid": 1, "ts": 1756660798492362, "dur": 106285, "ph": "X", "name": "BuildProgramContextConstructor", "args": {}}, {"pid": 35942, "tid": 1, "ts": 1756660798983460, "dur": 5887, "ph": "X", "name": "OutputData.Write", "args": {}}, {"pid": 35942, "tid": 1, "ts": 1756660798989350, "dur": 173509, "ph": "X", "name": "Backend.Write", "args": {}}, {"pid": 35942, "tid": 1, "ts": 1756660798991837, "dur": 72033, "ph": "X", "name": "JsonToString", "args": {}}, {"pid": 35942, "tid": 1, "ts": **********184849, "dur": 2004, "ph": "X", "name": "", "args": {}}, {"pid": 35942, "tid": 1, "ts": **********184226, "dur": 6175, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1756660796659176, "dur": 50, "ph": "X", "name": "IPC_Client_InitializeAndConnectToParent", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1756660796659281, "dur": 7282, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1756660796666581, "dur": 1462, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1756660796668228, "dur": 64, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": 1756660796668293, "dur": 207, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1756660796669090, "dur": 123, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_E15ABC52410B4A40.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1756660796669415, "dur": 63, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_767D1104FDA27930.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1756660796669614, "dur": 59, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_77DC58FFDE5AF86D.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1756660796670894, "dur": 4316, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_283DA3D7A0C52561.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1756660796675514, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Newtonsoft.Json.dll_88EDA310933505FB.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1756660796677360, "dur": 340, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1756660796677777, "dur": 64, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1756660796678770, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1756660796679682, "dur": 328, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Flow.ref.dll_1E6ED5409D07C9A3.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1756660796680421, "dur": 88, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1756660796680766, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1756660796681007, "dur": 65, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1756660796681622, "dur": 61, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1756660796682364, "dur": 118, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1756660796683130, "dur": 147, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1756660796683428, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1756660796684122, "dur": 102, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1756660796686724, "dur": 7978, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/9111662996337956171.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1756660796695752, "dur": 85, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Shaders.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1756660796699764, "dur": 76, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1756660796700284, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/15085861467720516389.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1756660796701774, "dur": 60, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.DocCodeExamples.dll"}}, {"pid": 12345, "tid": 0, "ts": 1756660796704510, "dur": 5278, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 0, "ts": 1756660796710251, "dur": 86, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": 1756660796668526, "dur": 42260, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1756660796710802, "dur": 187085, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1756660796897888, "dur": 199, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1756660796898109, "dur": 1436283, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1756660798334444, "dur": 88, "ph": "X", "name": "BuildQueueDestroyTail", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1756660798334596, "dur": 3363, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1756660796717321, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756660796717431, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program Files (x86)\\Unity\\Unity 3D\\2022.3.33f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityAnalyticsCommonModule.dll"}}, {"pid": 12345, "tid": 1, "ts": 1756660796717490, "dur": 1149, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program Files (x86)\\Unity\\Unity 3D\\2022.3.33f1c1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 1, "ts": 1756660796717424, "dur": 1216, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_A08CC7EE352FE9D2.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1756660796718810, "dur": 3115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756660796721941, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1756660796722238, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program Files (x86)\\Unity\\Unity 3D\\2022.3.33f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.ComponentModel.Composition.dll"}}, {"pid": 12345, "tid": 1, "ts": 1756660796722525, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program Files (x86)\\Unity\\Unity 3D\\2022.3.33f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Numerics.dll"}}, {"pid": 12345, "tid": 1, "ts": 1756660796722665, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program Files (x86)\\Unity\\Unity 3D\\2022.3.33f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Transactions.dll"}}, {"pid": 12345, "tid": 1, "ts": 1756660796722757, "dur": 2466, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program Files (x86)\\Unity\\Unity 3D\\2022.3.33f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Windows.dll"}}, {"pid": 12345, "tid": 1, "ts": 1756660796725306, "dur": 162, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program Files (x86)\\Unity\\Unity 3D\\2022.3.33f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Xml.Serialization.dll"}}, {"pid": 12345, "tid": 1, "ts": 1756660796725481, "dur": 152, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program Files (x86)\\Unity\\Unity 3D\\2022.3.33f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\Microsoft.Win32.Primitives.dll"}}, {"pid": 12345, "tid": 1, "ts": 1756660796725634, "dur": 67, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program Files (x86)\\Unity\\Unity 3D\\2022.3.33f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.AppContext.dll"}}, {"pid": 12345, "tid": 1, "ts": 1756660796725703, "dur": 197, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program Files (x86)\\Unity\\Unity 3D\\2022.3.33f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Buffers.dll"}}, {"pid": 12345, "tid": 1, "ts": 1756660796725917, "dur": 132, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program Files (x86)\\Unity\\Unity 3D\\2022.3.33f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Collections.Concurrent.dll"}}, {"pid": 12345, "tid": 1, "ts": 1756660796726762, "dur": 99, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program Files (x86)\\Unity\\Unity 3D\\2022.3.33f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Drawing.Primitives.dll"}}, {"pid": 12345, "tid": 1, "ts": 1756660796726862, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program Files (x86)\\Unity\\Unity 3D\\2022.3.33f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Dynamic.Runtime.dll"}}, {"pid": 12345, "tid": 1, "ts": 1756660796726919, "dur": 89, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program Files (x86)\\Unity\\Unity 3D\\2022.3.33f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Globalization.Calendars.dll"}}, {"pid": 12345, "tid": 1, "ts": 1756660796727010, "dur": 69, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program Files (x86)\\Unity\\Unity 3D\\2022.3.33f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Globalization.dll"}}, {"pid": 12345, "tid": 1, "ts": 1756660796727081, "dur": 80, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program Files (x86)\\Unity\\Unity 3D\\2022.3.33f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Globalization.Extensions.dll"}}, {"pid": 12345, "tid": 1, "ts": 1756660796727169, "dur": 109, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program Files (x86)\\Unity\\Unity 3D\\2022.3.33f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.Compression.dll"}}, {"pid": 12345, "tid": 1, "ts": 1756660796727308, "dur": 66, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program Files (x86)\\Unity\\Unity 3D\\2022.3.33f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.Compression.ZipFile.dll"}}, {"pid": 12345, "tid": 1, "ts": 1756660796727375, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program Files (x86)\\Unity\\Unity 3D\\2022.3.33f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.dll"}}, {"pid": 12345, "tid": 1, "ts": 1756660796727432, "dur": 606, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program Files (x86)\\Unity\\Unity 3D\\2022.3.33f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.FileSystem.dll"}}, {"pid": 12345, "tid": 1, "ts": 1756660796728039, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program Files (x86)\\Unity\\Unity 3D\\2022.3.33f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.FileSystem.DriveInfo.dll"}}, {"pid": 12345, "tid": 1, "ts": 1756660796728096, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program Files (x86)\\Unity\\Unity 3D\\2022.3.33f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.FileSystem.Primitives.dll"}}, {"pid": 12345, "tid": 1, "ts": 1756660796728189, "dur": 483, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program Files (x86)\\Unity\\Unity 3D\\2022.3.33f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.IsolatedStorage.dll"}}, {"pid": 12345, "tid": 1, "ts": 1756660796728673, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program Files (x86)\\Unity\\Unity 3D\\2022.3.33f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.MemoryMappedFiles.dll"}}, {"pid": 12345, "tid": 1, "ts": 1756660796728728, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program Files (x86)\\Unity\\Unity 3D\\2022.3.33f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.Pipes.dll"}}, {"pid": 12345, "tid": 1, "ts": 1756660796728828, "dur": 84, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program Files (x86)\\Unity\\Unity 3D\\2022.3.33f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Linq.dll"}}, {"pid": 12345, "tid": 1, "ts": 1756660796728913, "dur": 110, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program Files (x86)\\Unity\\Unity 3D\\2022.3.33f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Linq.Expressions.dll"}}, {"pid": 12345, "tid": 1, "ts": 1756660796729063, "dur": 64, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program Files (x86)\\Unity\\Unity 3D\\2022.3.33f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Linq.Queryable.dll"}}, {"pid": 12345, "tid": 1, "ts": 1756660796729167, "dur": 70, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program Files (x86)\\Unity\\Unity 3D\\2022.3.33f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.Http.dll"}}, {"pid": 12345, "tid": 1, "ts": 1756660796729284, "dur": 75, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program Files (x86)\\Unity\\Unity 3D\\2022.3.33f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.NetworkInformation.dll"}}, {"pid": 12345, "tid": 1, "ts": 1756660796729361, "dur": 91, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program Files (x86)\\Unity\\Unity 3D\\2022.3.33f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.Ping.dll"}}, {"pid": 12345, "tid": 1, "ts": 1756660796729582, "dur": 66, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program Files (x86)\\Unity\\Unity 3D\\2022.3.33f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.Sockets.dll"}}, {"pid": 12345, "tid": 1, "ts": 1756660796729650, "dur": 71, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program Files (x86)\\Unity\\Unity 3D\\2022.3.33f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.WebHeaderCollection.dll"}}, {"pid": 12345, "tid": 1, "ts": 1756660796729723, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program Files (x86)\\Unity\\Unity 3D\\2022.3.33f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.WebSockets.Client.dll"}}, {"pid": 12345, "tid": 1, "ts": 1756660796729778, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program Files (x86)\\Unity\\Unity 3D\\2022.3.33f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.WebSockets.dll"}}, {"pid": 12345, "tid": 1, "ts": 1756660796729835, "dur": 490, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program Files (x86)\\Unity\\Unity 3D\\2022.3.33f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Numerics.Vectors.dll"}}, {"pid": 12345, "tid": 1, "ts": 1756660796730451, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program Files (x86)\\Unity\\Unity 3D\\2022.3.33f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.Extensions.dll"}}, {"pid": 12345, "tid": 1, "ts": 1756660796730685, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program Files (x86)\\Unity\\Unity 3D\\2022.3.33f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.Serialization.Json.dll"}}, {"pid": 12345, "tid": 1, "ts": 1756660796730795, "dur": 71, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program Files (x86)\\Unity\\Unity 3D\\2022.3.33f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.Serialization.Xml.dll"}}, {"pid": 12345, "tid": 1, "ts": 1756660796731660, "dur": 85, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program Files (x86)\\Unity\\Unity 3D\\2022.3.33f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Xml.XDocument.dll"}}, {"pid": 12345, "tid": 1, "ts": 1756660796731746, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program Files (x86)\\Unity\\Unity 3D\\2022.3.33f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Xml.XmlDocument.dll"}}, {"pid": 12345, "tid": 1, "ts": 1756660796732120, "dur": 77, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Assertions\\ConstraintsExtensions.cs"}}, {"pid": 12345, "tid": 1, "ts": 1756660796732198, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Assertions\\InvalidSignatureException.cs"}}, {"pid": 12345, "tid": 1, "ts": 1756660796732262, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Assertions\\Is.cs"}}, {"pid": 12345, "tid": 1, "ts": 1756660796732446, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Assertions\\LogScope\\LogMatch.cs"}}, {"pid": 12345, "tid": 1, "ts": 1756660796732587, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Assertions\\UnexpectedLogMessageException.cs"}}, {"pid": 12345, "tid": 1, "ts": 1756660796732639, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Assertions\\UnhandledLogMessageException.cs"}}, {"pid": 12345, "tid": 1, "ts": 1756660796732738, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\ActionDelegator.cs"}}, {"pid": 12345, "tid": 1, "ts": 1756660796733180, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Commands\\BeforeAfterTestCommandBase.cs"}}, {"pid": 12345, "tid": 1, "ts": 1756660796733571, "dur": 220, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Commands\\OuterUnityTestActionCommand.cs"}}, {"pid": 12345, "tid": 1, "ts": 1756660796734147, "dur": 130, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\IStateSerializer.cs"}}, {"pid": 12345, "tid": 1, "ts": 1756660796734485, "dur": 66, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Runner\\FailCommand.cs"}}, {"pid": 12345, "tid": 1, "ts": 1756660796734553, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Runner\\IEnumerableTestMethodCommand.cs"}}, {"pid": 12345, "tid": 1, "ts": 1756660796734615, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Runner\\PlaymodeWorkItemFactory.cs"}}, {"pid": 12345, "tid": 1, "ts": 1756660796734750, "dur": 436, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Runner\\UnityLogCheckDelegatingCommand.cs"}}, {"pid": 12345, "tid": 1, "ts": 1756660796735187, "dur": 182, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Runner\\UnityTestAssemblyRunner.cs"}}, {"pid": 12345, "tid": 1, "ts": 1756660796735371, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Runner\\UnityTestExecutionContext.cs"}}, {"pid": 12345, "tid": 1, "ts": 1756660796735470, "dur": 72, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Runner\\UnityWorkItemDataHolder.cs"}}, {"pid": 12345, "tid": 1, "ts": 1756660796735748, "dur": 100, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\TestRunner\\Callbacks\\PlayModeRunnerCallback.cs"}}, {"pid": 12345, "tid": 1, "ts": 1756660796735849, "dur": 64, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\TestRunner\\Callbacks\\RemoteTestResultSender.cs"}}, {"pid": 12345, "tid": 1, "ts": 1756660796735914, "dur": 77, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\TestRunner\\Callbacks\\TestResultRenderer.cs"}}, {"pid": 12345, "tid": 1, "ts": 1756660796735993, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\TestRunner\\Callbacks\\TestResultRendererCallback.cs"}}, {"pid": 12345, "tid": 1, "ts": 1756660796736096, "dur": 94, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\TestRunner\\Messages\\IEditModeTestYieldInstruction.cs"}}, {"pid": 12345, "tid": 1, "ts": 1756660796736191, "dur": 140, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\TestRunner\\PlaymodeTestsController.cs"}}, {"pid": 12345, "tid": 1, "ts": 1756660796736379, "dur": 94, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\TestRunner\\RemoteHelpers\\IRemoteTestResultDataFactory.cs"}}, {"pid": 12345, "tid": 1, "ts": 1756660796736475, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\TestRunner\\RemoteHelpers\\PlayerConnectionMessageIds.cs"}}, {"pid": 12345, "tid": 1, "ts": 1756660796736625, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\TestRunner\\RemoteHelpers\\RemoteTestResultDataFactory.cs"}}, {"pid": 12345, "tid": 1, "ts": 1756660796736807, "dur": 126, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\TestRunner\\TestEnumeratorWrapper.cs"}}, {"pid": 12345, "tid": 1, "ts": 1756660796737219, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Utils\\AssemblyProvider\\PlayerTestAssemblyProvider.cs"}}, {"pid": 12345, "tid": 1, "ts": 1756660796737790, "dur": 67, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Utils\\StacktraceFilter.cs"}}, {"pid": 12345, "tid": 1, "ts": 1756660796737858, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Utils\\TestRunCallbackAttribute.cs"}}, {"pid": 12345, "tid": 1, "ts": 1756660796722067, "dur": 16146, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1756660796738214, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756660796738374, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1756660796738776, "dur": 109, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program Files (x86)\\Unity\\Unity 3D\\2022.3.33f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Collections.Concurrent.dll"}}, {"pid": 12345, "tid": 1, "ts": 1756660796738886, "dur": 88, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program Files (x86)\\Unity\\Unity 3D\\2022.3.33f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Collections.dll"}}, {"pid": 12345, "tid": 1, "ts": 1756660796738976, "dur": 66, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program Files (x86)\\Unity\\Unity 3D\\2022.3.33f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Collections.NonGeneric.dll"}}, {"pid": 12345, "tid": 1, "ts": 1756660796739085, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program Files (x86)\\Unity\\Unity 3D\\2022.3.33f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ComponentModel.Annotations.dll"}}, {"pid": 12345, "tid": 1, "ts": 1756660796739410, "dur": 320, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program Files (x86)\\Unity\\Unity 3D\\2022.3.33f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Diagnostics.Debug.dll"}}, {"pid": 12345, "tid": 1, "ts": 1756660796739817, "dur": 847, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program Files (x86)\\Unity\\Unity 3D\\2022.3.33f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Diagnostics.StackTrace.dll"}}, {"pid": 12345, "tid": 1, "ts": 1756660796740667, "dur": 94, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program Files (x86)\\Unity\\Unity 3D\\2022.3.33f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Diagnostics.TextWriterTraceListener.dll"}}, {"pid": 12345, "tid": 1, "ts": 1756660796740809, "dur": 104, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program Files (x86)\\Unity\\Unity 3D\\2022.3.33f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Diagnostics.TraceSource.dll"}}, {"pid": 12345, "tid": 1, "ts": 1756660796740915, "dur": 66, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program Files (x86)\\Unity\\Unity 3D\\2022.3.33f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Drawing.Primitives.dll"}}, {"pid": 12345, "tid": 1, "ts": 1756660796741059, "dur": 105, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program Files (x86)\\Unity\\Unity 3D\\2022.3.33f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Globalization.dll"}}, {"pid": 12345, "tid": 1, "ts": 1756660796741215, "dur": 179, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program Files (x86)\\Unity\\Unity 3D\\2022.3.33f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.Compression.ZipFile.dll"}}, {"pid": 12345, "tid": 1, "ts": 1756660796741470, "dur": 364, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program Files (x86)\\Unity\\Unity 3D\\2022.3.33f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.FileSystem.DriveInfo.dll"}}, {"pid": 12345, "tid": 1, "ts": 1756660796742016, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program Files (x86)\\Unity\\Unity 3D\\2022.3.33f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.UnmanagedMemoryStream.dll"}}, {"pid": 12345, "tid": 1, "ts": 1756660796742181, "dur": 573, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program Files (x86)\\Unity\\Unity 3D\\2022.3.33f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Linq.Queryable.dll"}}, {"pid": 12345, "tid": 1, "ts": 1756660796742755, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program Files (x86)\\Unity\\Unity 3D\\2022.3.33f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Memory.dll"}}, {"pid": 12345, "tid": 1, "ts": 1756660796745155, "dur": 117, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program Files (x86)\\Unity\\Unity 3D\\2022.3.33f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Xml.XPath.XDocument.dll"}}, {"pid": 12345, "tid": 1, "ts": 1756660796745274, "dur": 94, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program Files (x86)\\Unity\\Unity 3D\\2022.3.33f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Microsoft.CSharp.dll"}}, {"pid": 12345, "tid": 1, "ts": 1756660796745762, "dur": 174, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program Files (x86)\\Unity\\Unity 3D\\2022.3.33f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.Numerics.dll"}}, {"pid": 12345, "tid": 1, "ts": 1756660796746088, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program Files (x86)\\Unity\\Unity 3D\\2022.3.33f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.Xml.Linq.dll"}}, {"pid": 12345, "tid": 1, "ts": 1756660796746810, "dur": 688, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEditor.TestRunner\\Api\\TestAdaptor.cs"}}, {"pid": 12345, "tid": 1, "ts": 1756660796748694, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEditor.TestRunner\\GUI\\TestListTreeView\\TestListTreeViewGUI.cs"}}, {"pid": 12345, "tid": 1, "ts": 1756660796749121, "dur": 126, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEditor.TestRunner\\NUnitExtension\\TestRunnerStateSerializer.cs"}}, {"pid": 12345, "tid": 1, "ts": 1756660796749258, "dur": 70, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEditor.TestRunner\\RequireApiProfileAttribute.cs"}}, {"pid": 12345, "tid": 1, "ts": 1756660796749407, "dur": 64, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEditor.TestRunner\\TestLaunchers\\AttributeFinderBase.cs"}}, {"pid": 12345, "tid": 1, "ts": 1756660796749822, "dur": 66, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEditor.TestRunner\\TestLaunchers\\PlatformSetup\\StadiaPlatformSetup.cs"}}, {"pid": 12345, "tid": 1, "ts": 1756660796749890, "dur": 75, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEditor.TestRunner\\TestLaunchers\\PlatformSetup\\SwitchPlatformSetup.cs"}}, {"pid": 12345, "tid": 1, "ts": 1756660796749967, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEditor.TestRunner\\TestLaunchers\\PlatformSetup\\UwpPlatformSetup.cs"}}, {"pid": 12345, "tid": 1, "ts": 1756660796750069, "dur": 69, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEditor.TestRunner\\TestLaunchers\\PlayerLauncher.cs"}}, {"pid": 12345, "tid": 1, "ts": 1756660796750140, "dur": 79, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEditor.TestRunner\\TestLaunchers\\PlayerLauncherBuildOptions.cs"}}, {"pid": 12345, "tid": 1, "ts": 1756660796750225, "dur": 102, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEditor.TestRunner\\TestLaunchers\\PlayerLauncherContextSettings.cs"}}, {"pid": 12345, "tid": 1, "ts": 1756660796750400, "dur": 71, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEditor.TestRunner\\TestLaunchers\\PostbuildCleanupAttributeFinder.cs"}}, {"pid": 12345, "tid": 1, "ts": 1756660796751750, "dur": 90, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEditor.TestRunner\\TestRunner\\Messages\\EnterPlayMode.cs"}}, {"pid": 12345, "tid": 1, "ts": 1756660796751852, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEditor.TestRunner\\TestRunner\\Messages\\ExitPlayMode.cs"}}, {"pid": 12345, "tid": 1, "ts": 1756660796751908, "dur": 73, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEditor.TestRunner\\TestRunner\\Messages\\RecompileScripts.cs"}}, {"pid": 12345, "tid": 1, "ts": 1756660796751982, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEditor.TestRunner\\TestRunner\\Messages\\WaitForDomainReload.cs"}}, {"pid": 12345, "tid": 1, "ts": 1756660796752082, "dur": 67, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEditor.TestRunner\\TestRunner\\Utils\\EditorAssembliesProxy.cs"}}, {"pid": 12345, "tid": 1, "ts": 1756660796752150, "dur": 68, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEditor.TestRunner\\TestRunner\\Utils\\EditorAssemblyWrapper.cs"}}, {"pid": 12345, "tid": 1, "ts": 1756660796752219, "dur": 266, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEditor.TestRunner\\TestRunner\\Utils\\EditorCompilationInterfaceProxy.cs"}}, {"pid": 12345, "tid": 1, "ts": 1756660796752486, "dur": 263, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEditor.TestRunner\\TestRunner\\Utils\\EditorLoadedTestAssemblyProvider.cs"}}, {"pid": 12345, "tid": 1, "ts": 1756660796752750, "dur": 216, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEditor.TestRunner\\TestRunner\\Utils\\IEditorAssembliesProxy.cs"}}, {"pid": 12345, "tid": 1, "ts": 1756660796752968, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEditor.TestRunner\\TestRunner\\Utils\\IEditorCompilationInterfaceProxy.cs"}}, {"pid": 12345, "tid": 1, "ts": 1756660796738494, "dur": 15495, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1756660796753990, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756660796754158, "dur": 92, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1756660796754291, "dur": 1662, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1756660796755954, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756660796756138, "dur": 304, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1756660796756495, "dur": 109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1756660796756781, "dur": 113, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program Files (x86)\\Unity\\Unity 3D\\2022.3.33f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestAssetBundleModule.dll"}}, {"pid": 12345, "tid": 1, "ts": 1756660796757185, "dur": 278, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.8\\Runtime\\VisualScripting.Core\\Attributes\\VisualScriptingHelpURLAttribute.cs"}}, {"pid": 12345, "tid": 1, "ts": 1756660796757511, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.8\\Runtime\\VisualScripting.Core\\Cloning\\Cloners\\AnimationCurveCloner.cs"}}, {"pid": 12345, "tid": 1, "ts": 1756660796758799, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.8\\Runtime\\VisualScripting.Core\\Collections\\VariantList.cs"}}, {"pid": 12345, "tid": 1, "ts": 1756660796758922, "dur": 519, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.8\\Runtime\\VisualScripting.Core\\Connections\\ConnectionCollectionBase.cs"}}, {"pid": 12345, "tid": 1, "ts": 1756660796759446, "dur": 69, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.8\\Runtime\\VisualScripting.Core\\Connections\\GraphConnectionCollection.cs"}}, {"pid": 12345, "tid": 1, "ts": 1756660796759516, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.8\\Runtime\\VisualScripting.Core\\Connections\\IConnection.cs"}}, {"pid": 12345, "tid": 1, "ts": 1756660796759608, "dur": 70, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.8\\Runtime\\VisualScripting.Core\\Connections\\InvalidConnectionException.cs"}}, {"pid": 12345, "tid": 1, "ts": 1756660796759777, "dur": 155, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.8\\Runtime\\VisualScripting.Core\\Dependencies\\AssemblyQualifiedNameParser\\ParsedAssemblyQualifiedName.cs"}}, {"pid": 12345, "tid": 1, "ts": 1756660796759979, "dur": 108, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.8\\Runtime\\VisualScripting.Core\\Dependencies\\FullSerializer\\Converters\\fsDateConverter.cs"}}, {"pid": 12345, "tid": 1, "ts": 1756660796760088, "dur": 91, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.8\\Runtime\\VisualScripting.Core\\Dependencies\\FullSerializer\\Converters\\fsDictionaryConverter.cs"}}, {"pid": 12345, "tid": 1, "ts": 1756660796760181, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.8\\Runtime\\VisualScripting.Core\\Dependencies\\FullSerializer\\Converters\\fsEnumConverter.cs"}}, {"pid": 12345, "tid": 1, "ts": 1756660796760233, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.8\\Runtime\\VisualScripting.Core\\Dependencies\\FullSerializer\\Converters\\fsForwardConverter.cs"}}, {"pid": 12345, "tid": 1, "ts": 1756660796760296, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.8\\Runtime\\VisualScripting.Core\\Dependencies\\FullSerializer\\Converters\\fsGuidConverter.cs"}}, {"pid": 12345, "tid": 1, "ts": 1756660796760366, "dur": 150, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.8\\Runtime\\VisualScripting.Core\\Dependencies\\FullSerializer\\Converters\\fsIEnumerableConverter.cs"}}, {"pid": 12345, "tid": 1, "ts": 1756660796760518, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.8\\Runtime\\VisualScripting.Core\\Dependencies\\FullSerializer\\Converters\\fsKeyValuePairConverter.cs"}}, {"pid": 12345, "tid": 1, "ts": 1756660796760574, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.8\\Runtime\\VisualScripting.Core\\Dependencies\\FullSerializer\\Converters\\fsNullableConverter.cs"}}, {"pid": 12345, "tid": 1, "ts": 1756660796760676, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.8\\Runtime\\VisualScripting.Core\\Dependencies\\FullSerializer\\Converters\\fsReflectedConverter.cs"}}, {"pid": 12345, "tid": 1, "ts": 1756660796760731, "dur": 143, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.8\\Runtime\\VisualScripting.Core\\Dependencies\\FullSerializer\\Converters\\fsTypeConverter.cs"}}, {"pid": 12345, "tid": 1, "ts": 1756660796760876, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.8\\Runtime\\VisualScripting.Core\\Dependencies\\FullSerializer\\Converters\\fsWeakReferenceConverter.cs"}}, {"pid": 12345, "tid": 1, "ts": 1756660796761204, "dur": 179, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.8\\Runtime\\VisualScripting.Core\\Dependencies\\FullSerializer\\Converters\\Unity\\Keyframe_DirectConverter.cs"}}, {"pid": 12345, "tid": 1, "ts": 1756660796761385, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.8\\Runtime\\VisualScripting.Core\\Dependencies\\FullSerializer\\Converters\\Unity\\LayerMask_DirectConverter.cs"}}, {"pid": 12345, "tid": 1, "ts": 1756660796761488, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.8\\Runtime\\VisualScripting.Core\\Dependencies\\FullSerializer\\Converters\\Unity\\Rect_DirectConverter.cs"}}, {"pid": 12345, "tid": 1, "ts": 1756660796762289, "dur": 369, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.8\\Runtime\\VisualScripting.Core\\Dependencies\\FullSerializer\\fsResult.cs"}}, {"pid": 12345, "tid": 1, "ts": 1756660796763579, "dur": 19538, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.8\\Runtime\\VisualScripting.Core\\EditorBinding\\Inspector\\InspectorDelayedAttribute.cs"}}, {"pid": 12345, "tid": 1, "ts": 1756660796783281, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.8\\Runtime\\VisualScripting.Core\\EditorBinding\\Inspector\\InspectorToggleLeftAttribute.cs"}}, {"pid": 12345, "tid": 1, "ts": 1756660796785345, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.8\\Runtime\\VisualScripting.Core\\Graphs\\IGraphData.cs"}}, {"pid": 12345, "tid": 1, "ts": 1756660796787255, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.8\\Runtime\\VisualScripting.Core\\Listeners\\MonoBehaviour\\UnityOnTriggerExitMListener.cs"}}, {"pid": 12345, "tid": 1, "ts": 1756660796787308, "dur": 654, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.8\\Runtime\\VisualScripting.Core\\Listeners\\MonoBehaviour\\UnityOnTriggerStay2DMListener.cs"}}, {"pid": 12345, "tid": 1, "ts": 1756660796788000, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.8\\Runtime\\VisualScripting.Core\\Listeners\\MonoBehaviour\\UnityOnTriggerStayMListener.cs"}}, {"pid": 12345, "tid": 1, "ts": 1756660796788292, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.8\\Runtime\\VisualScripting.Core\\Listeners\\UI\\UnityOnSliderValueChangedMessageListener.cs"}}, {"pid": 12345, "tid": 1, "ts": 1756660796788519, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.8\\Runtime\\VisualScripting.Core\\Listeners\\UIInterfaces\\UnityOnDragMessageListener.cs"}}, {"pid": 12345, "tid": 1, "ts": 1756660796788619, "dur": 72, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.8\\Runtime\\VisualScripting.Core\\Listeners\\UIInterfaces\\UnityOnEndDragMessageListener.cs"}}, {"pid": 12345, "tid": 1, "ts": 1756660796788776, "dur": 1221, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.8\\Runtime\\VisualScripting.Core\\Listeners\\UIInterfaces\\UnityOnPointerDownMessageListener.cs"}}, {"pid": 12345, "tid": 1, "ts": 1756660796789999, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.8\\Runtime\\VisualScripting.Core\\Listeners\\UIInterfaces\\UnityOnPointerEnterMessageListener.cs"}}, {"pid": 12345, "tid": 1, "ts": 1756660796790142, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.8\\Runtime\\VisualScripting.Core\\Listeners\\UIInterfaces\\UnityOnScrollMessageListener.cs"}}, {"pid": 12345, "tid": 1, "ts": 1756660796790202, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.8\\Runtime\\VisualScripting.Core\\Listeners\\UIInterfaces\\UnityOnSelectMessageListener.cs"}}, {"pid": 12345, "tid": 1, "ts": 1756660796790352, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.8\\Runtime\\VisualScripting.Core\\Machines\\IMachine.cs"}}, {"pid": 12345, "tid": 1, "ts": 1756660796790412, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.8\\Runtime\\VisualScripting.Core\\Machines\\Machine.cs"}}, {"pid": 12345, "tid": 1, "ts": 1756660796790472, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.8\\Runtime\\VisualScripting.Core\\Macros\\IMacro.cs"}}, {"pid": 12345, "tid": 1, "ts": 1756660796790530, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.8\\Runtime\\VisualScripting.Core\\Macros\\Macro.cs"}}, {"pid": 12345, "tid": 1, "ts": 1756660796792356, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.8\\Runtime\\VisualScripting.Core\\Reflection\\Operators\\ModuloHandler.cs"}}, {"pid": 12345, "tid": 1, "ts": 1756660796793121, "dur": 79, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.8\\Runtime\\VisualScripting.Core\\Reflection\\Optimization\\InstanceActionInvoker_2.cs"}}, {"pid": 12345, "tid": 1, "ts": 1756660796795556, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.8\\Runtime\\VisualScripting.Core\\SerializedProperties\\ISerializedPropertyProvider.cs"}}, {"pid": 12345, "tid": 1, "ts": 1756660796795687, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.8\\Runtime\\VisualScripting.Core\\StickyNote\\StickyNote.cs"}}, {"pid": 12345, "tid": 1, "ts": 1756660796795780, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.8\\Runtime\\VisualScripting.Core\\Unity\\ISingleton.cs"}}, {"pid": 12345, "tid": 1, "ts": 1756660796795836, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.8\\Runtime\\VisualScripting.Core\\Unity\\IUnityObjectOwnable.cs"}}, {"pid": 12345, "tid": 1, "ts": 1756660796797138, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.8\\Runtime\\VisualScripting.Core\\Variables\\ObjectVariables.cs"}}, {"pid": 12345, "tid": 1, "ts": 1756660796756656, "dur": 40933, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1756660796797590, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756660796797768, "dur": 88, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1756660796799172, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.8\\Runtime\\VisualScripting.Flow\\Dependencies\\NCalc\\TernaryExpression.cs"}}, {"pid": 12345, "tid": 1, "ts": 1756660796800056, "dur": 116, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.8\\Runtime\\VisualScripting.Flow\\Framework\\Collections\\Dictionaries\\AddDictionaryItem.cs"}}, {"pid": 12345, "tid": 1, "ts": 1756660796800219, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.8\\Runtime\\VisualScripting.Flow\\Framework\\Collections\\Dictionaries\\CreateDictionary.cs"}}, {"pid": 12345, "tid": 1, "ts": 1756660796802709, "dur": 116, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.8\\Runtime\\VisualScripting.Flow\\Framework\\Events\\GUI\\OnGUI.cs"}}, {"pid": 12345, "tid": 1, "ts": 1756660796803094, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.8\\Runtime\\VisualScripting.Flow\\Framework\\Events\\GUI\\OnPointerUp.cs"}}, {"pid": 12345, "tid": 1, "ts": 1756660796803200, "dur": 66, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.8\\Runtime\\VisualScripting.Flow\\Framework\\Events\\GUI\\OnScrollbarValueChanged.cs"}}, {"pid": 12345, "tid": 1, "ts": 1756660796803268, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.8\\Runtime\\VisualScripting.Flow\\Framework\\Events\\GUI\\OnScrollRectValueChanged.cs"}}, {"pid": 12345, "tid": 1, "ts": 1756660796806172, "dur": 69, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.8\\Runtime\\VisualScripting.Flow\\Framework\\Logic\\Negate.cs"}}, {"pid": 12345, "tid": 1, "ts": 1756660796806242, "dur": 107, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.8\\Runtime\\VisualScripting.Flow\\Framework\\Logic\\NotApproximatelyEqual.cs"}}, {"pid": 12345, "tid": 1, "ts": 1756660796807771, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.8\\Runtime\\VisualScripting.Flow\\Framework\\Math\\Scalar\\ScalarRoot.cs"}}, {"pid": 12345, "tid": 1, "ts": 1756660796797897, "dur": 15341, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1756660796813239, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756660796813422, "dur": 92, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1756660796814482, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.8\\Runtime\\VisualScripting.State\\OnExitState.cs"}}, {"pid": 12345, "tid": 1, "ts": 1756660796814536, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.8\\Runtime\\VisualScripting.State\\Properties\\AssemblyInfo.cs"}}, {"pid": 12345, "tid": 1, "ts": 1756660796813567, "dur": 1630, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1756660796815198, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756660796815358, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UltimateEditorEnhancer.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1756660796815483, "dur": 368, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UltimateEditorEnhancer.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1756660796815851, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756660796815962, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1756660796816091, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1756660796816206, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1756660796816325, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1756660796816449, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1756660796816570, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1756660796816688, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1756660796816805, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1756660796817775, "dur": 5514, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ide.rider@3.0.36\\Rider\\Editor\\ProjectGeneration\\LastWriteTracker.cs"}}, {"pid": 12345, "tid": 1, "ts": 1756660796823292, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ide.rider@3.0.36\\Rider\\Editor\\ProjectGeneration\\PackageManagerTracker.cs"}}, {"pid": 12345, "tid": 1, "ts": 1756660796823530, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ide.rider@3.0.36\\Rider\\Editor\\Properties\\AssemblyInfo.cs"}}, {"pid": 12345, "tid": 1, "ts": 1756660796823618, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ide.rider@3.0.36\\Rider\\Editor\\RiderScriptEditor.cs"}}, {"pid": 12345, "tid": 1, "ts": 1756660796816919, "dur": 7126, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1756660796824046, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756660796824160, "dur": 423, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1756660796824584, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756660796824719, "dur": 376, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1756660796825096, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756660796825215, "dur": 393, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1756660796825609, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756660796825713, "dur": 387, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1756660796826101, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756660796826223, "dur": 394, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1756660796826618, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756660796826742, "dur": 693, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1756660796827436, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756660796827557, "dur": 379, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1756660796827937, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756660796828068, "dur": 90, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UltimateEditorEnhancer-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1756660796828200, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/VTabs.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1756660796828317, "dur": 320, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/VTabs.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1756660796828637, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756660796828758, "dur": 759, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UltimateEditorEnhancer-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1756660796829517, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756660796830025, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.DocCodeExamples.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1756660796830140, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.Entities.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1756660796830268, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1756660796830388, "dur": 307, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.DocCodeExamples.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1756660796830695, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756660796830803, "dur": 320, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1756660796831123, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756660796831230, "dur": 325, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.Entities.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1756660796831556, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756660796831990, "dur": 183, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756660796832174, "dur": 706, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756660796832880, "dur": 117, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756660796833090, "dur": 243, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756660796833333, "dur": 693, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756660796834027, "dur": 681, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756660796834708, "dur": 672, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756660796835380, "dur": 661, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756660796836041, "dur": 78, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756660796836341, "dur": 245, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756660796836586, "dur": 686, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756660796837273, "dur": 682, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756660796837955, "dur": 893, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756660796838849, "dur": 695, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756660796839545, "dur": 773, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756660796840319, "dur": 699, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756660796841506, "dur": 620, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.8\\Editor\\VisualScripting.Core\\Platforms\\FieldInfoStubWriter.cs"}}, {"pid": 12345, "tid": 1, "ts": 1756660796841019, "dur": 1210, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756660796842388, "dur": 1964, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756660796844354, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1756660796844454, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756660796844514, "dur": 388, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1756660796844903, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756660796845098, "dur": 1490, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756660796846589, "dur": 112, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor-firstpass.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1756660796846746, "dur": 93, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1756660796846882, "dur": 102, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1756660796847022, "dur": 651, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756660796847673, "dur": 352, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor-firstpass.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1756660796848025, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756660796848142, "dur": 49740, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756660796717321, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756660796717438, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program Files (x86)\\Unity\\Unity 3D\\2022.3.33f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UmbraModule.dll"}}, {"pid": 12345, "tid": 2, "ts": 1756660796717495, "dur": 147, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program Files (x86)\\Unity\\Unity 3D\\2022.3.33f1c1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 2, "ts": 1756660796717428, "dur": 215, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UmbraModule.dll_F09C773B1ADF01A7.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1756660796717644, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756660796717789, "dur": 494, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program Files (x86)\\Unity\\Unity 3D\\2022.3.33f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TerrainPhysicsModule.dll"}}, {"pid": 12345, "tid": 2, "ts": 1756660796717787, "dur": 499, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_CB694B1F65AF7E88.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1756660796718292, "dur": 140, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756660796718529, "dur": 69, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program Files (x86)\\Unity\\Unity 3D\\2022.3.33f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ProfilerModule.dll"}}, {"pid": 12345, "tid": 2, "ts": 1756660796718527, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ProfilerModule.dll_8B98576B8D1CDC1F.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1756660796718771, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756660796719766, "dur": 94, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program Files (x86)\\Unity\\Unity 3D\\2022.3.33f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.LocalizationModule.dll"}}, {"pid": 12345, "tid": 2, "ts": 1756660796719764, "dur": 99, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_9F43FBBA8A0F0FFF.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1756660796719863, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756660796719968, "dur": 67, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program Files (x86)\\Unity\\Unity 3D\\2022.3.33f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityTestProtocolModule.dll"}}, {"pid": 12345, "tid": 2, "ts": 1756660796719966, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_45C9294B7BD2139D.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1756660796720087, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program Files (x86)\\Unity\\Unity 3D\\2022.3.33f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestModule.dll"}}, {"pid": 12345, "tid": 2, "ts": 1756660796720085, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_C89138171B90234E.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1756660796720151, "dur": 150, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756660796720309, "dur": 77, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program Files (x86)\\Unity\\Unity 3D\\2022.3.33f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VehiclesModule.dll"}}, {"pid": 12345, "tid": 2, "ts": 1756660796720307, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_D11517B46DED24AD.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1756660796720434, "dur": 64, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program Files (x86)\\Unity\\Unity 3D\\2022.3.33f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VirtualTexturingModule.dll"}}, {"pid": 12345, "tid": 2, "ts": 1756660796720432, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_748140C0F5F44C13.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1756660796720501, "dur": 145, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756660796720818, "dur": 532, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program Files (x86)\\Unity\\Unity 3D\\2022.3.33f1c1\\Editor\\Data\\PlaybackEngines\\WindowsStandaloneSupport\\UnityEditor.WindowsStandalone.Extensions.dll"}}, {"pid": 12345, "tid": 2, "ts": 1756660796720816, "dur": 537, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WindowsStandalone.Extensions.dll_EA6DB8F3FED6BDB6.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1756660796721444, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756660796721521, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756660796721860, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756660796722017, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1756660796723262, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756660796723394, "dur": 1168, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756660796724577, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756660796724688, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756660796724776, "dur": 159, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756660796724947, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756660796725140, "dur": 871, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756660796726806, "dur": 669, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756660796727665, "dur": 426, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 2, "ts": 1756660796728095, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756660796728174, "dur": 272, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756660796728483, "dur": 162, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756660796728646, "dur": 122, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 2, "ts": 1756660796728771, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756660796728836, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756660796729227, "dur": 262, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756660796729720, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756660796729775, "dur": 88, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 2, "ts": 1756660796729863, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756660796729923, "dur": 112, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1756660796730036, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756660796730098, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756660796730758, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1756660796730816, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756660796730918, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756660796731077, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756660796731148, "dur": 163, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UltimateEditorEnhancer-Editor.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1756660796731409, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-firstpass.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1756660796731671, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756660796731798, "dur": 498, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756660796732353, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756660796732429, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756660796732589, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756660796732809, "dur": 135, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756660796733531, "dur": 202, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756660796733828, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756660796733925, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756660796734059, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756660796734154, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756660796734241, "dur": 368, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756660796734614, "dur": 929, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756660796735772, "dur": 720, "ph": "X", "name": "File", "args": {"detail": "Assets\\Plugins\\Demigiant\\DOTweenPro\\Editor\\DOTweenAnimationInspector.cs"}}, {"pid": 12345, "tid": 2, "ts": 1756660796735543, "dur": 1465, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756660796737009, "dur": 767, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756660796737777, "dur": 826, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756660796738793, "dur": 571, "ph": "X", "name": "File", "args": {"detail": "Assets\\Plugins\\Infinity Code\\Ultimate Editor Enhancer\\Scripts\\Editor\\Scene View\\MoveToPoint.cs"}}, {"pid": 12345, "tid": 2, "ts": 1756660796739436, "dur": 1801, "ph": "X", "name": "File", "args": {"detail": "Assets\\Plugins\\Infinity Code\\Ultimate Editor Enhancer\\Scripts\\Editor\\Scene View\\HighJumpToPoint.cs"}}, {"pid": 12345, "tid": 2, "ts": 1756660796738689, "dur": 3446, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756660796742135, "dur": 772, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756660796742908, "dur": 773, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756660796743681, "dur": 664, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756660796744345, "dur": 693, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756660796745039, "dur": 685, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756660796745724, "dur": 677, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756660796746401, "dur": 664, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756660796747066, "dur": 709, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756660796747776, "dur": 689, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756660796748466, "dur": 758, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756660796749225, "dur": 868, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756660796750093, "dur": 768, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756660796750861, "dur": 652, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756660796751514, "dur": 1249, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756660796752764, "dur": 900, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756660796753665, "dur": 662, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756660796754328, "dur": 882, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756660796756081, "dur": 1025, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.textmeshpro@3.0.9\\Scripts\\Editor\\TMP_PostBuildProcessHandler.cs"}}, {"pid": 12345, "tid": 2, "ts": 1756660796755210, "dur": 1943, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756660796757155, "dur": 129, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1756660796757372, "dur": 100, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program Files (x86)\\Unity\\Unity 3D\\2022.3.33f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AccessibilityModule.dll"}}, {"pid": 12345, "tid": 2, "ts": 1756660796757760, "dur": 213, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core@14.0.11\\Runtime\\AssemblyInfo.cs"}}, {"pid": 12345, "tid": 2, "ts": 1756660796758923, "dur": 525, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core@14.0.11\\Runtime\\Debugging\\DebugDisplaySettingsVolumes.cs"}}, {"pid": 12345, "tid": 2, "ts": 1756660796759453, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core@14.0.11\\Runtime\\Debugging\\DebugFrameTiming.cs"}}, {"pid": 12345, "tid": 2, "ts": 1756660796759516, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core@14.0.11\\Runtime\\Debugging\\DebugManager.Actions.cs"}}, {"pid": 12345, "tid": 2, "ts": 1756660796759812, "dur": 581, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core@14.0.11\\Runtime\\Debugging\\DebugUI.Panel.cs"}}, {"pid": 12345, "tid": 2, "ts": 1756660796760395, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core@14.0.11\\Runtime\\Debugging\\DebugUpdater.cs"}}, {"pid": 12345, "tid": 2, "ts": 1756660796760457, "dur": 94, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core@14.0.11\\Runtime\\Debugging\\FrameTiming\\FrameTimeBottleneck.cs"}}, {"pid": 12345, "tid": 2, "ts": 1756660796760726, "dur": 160, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core@14.0.11\\Runtime\\Debugging\\IDebugDisplaySettingsQuery.cs"}}, {"pid": 12345, "tid": 2, "ts": 1756660796760930, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core@14.0.11\\Runtime\\Debugging\\MousePositionDebug.cs"}}, {"pid": 12345, "tid": 2, "ts": 1756660796761206, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core@14.0.11\\Runtime\\Debugging\\Prefabs\\Scripts\\DebugUIHandlerEnumField.cs"}}, {"pid": 12345, "tid": 2, "ts": 1756660796761537, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core@14.0.11\\Runtime\\Debugging\\Prefabs\\Scripts\\DebugUIHandlerIndirectToggle.cs"}}, {"pid": 12345, "tid": 2, "ts": 1756660796761591, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core@14.0.11\\Runtime\\Debugging\\Prefabs\\Scripts\\DebugUIHandlerIntField.cs"}}, {"pid": 12345, "tid": 2, "ts": 1756660796762688, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core@14.0.11\\Runtime\\Debugging\\Prefabs\\Scripts\\DebugUIHandlerVector2.cs"}}, {"pid": 12345, "tid": 2, "ts": 1756660796763575, "dur": 7571, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core@14.0.11\\Runtime\\Lighting\\ProbeVolume\\ProbeVolumeAsset.cs"}}, {"pid": 12345, "tid": 2, "ts": 1756660796771199, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core@14.0.11\\Runtime\\Lighting\\ProbeVolume\\ProbeVolumeGIContributor.cs"}}, {"pid": 12345, "tid": 2, "ts": 1756660796771378, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core@14.0.11\\Runtime\\Lighting\\ProbeVolume\\ShaderVariablesProbeVolumes.cs"}}, {"pid": 12345, "tid": 2, "ts": 1756660796771589, "dur": 145, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core@14.0.11\\Runtime\\PostProcessing\\LensFlareCommonSRP.cs"}}, {"pid": 12345, "tid": 2, "ts": 1756660796771743, "dur": 505, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core@14.0.11\\Runtime\\PostProcessing\\LensFlareComponentSRP.cs"}}, {"pid": 12345, "tid": 2, "ts": 1756660796772438, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core@14.0.11\\Runtime\\RenderGraph\\RenderGraphObjectPool.cs"}}, {"pid": 12345, "tid": 2, "ts": 1756660796772579, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core@14.0.11\\Runtime\\RenderGraph\\RenderGraphResourceComputeBuffer.cs"}}, {"pid": 12345, "tid": 2, "ts": 1756660796772642, "dur": 702, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core@14.0.11\\Runtime\\RenderGraph\\RenderGraphResourcePool.cs"}}, {"pid": 12345, "tid": 2, "ts": 1756660796773346, "dur": 191, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core@14.0.11\\Runtime\\RenderGraph\\RenderGraphResourceRegistry.cs"}}, {"pid": 12345, "tid": 2, "ts": 1756660796773734, "dur": 82, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core@14.0.11\\Runtime\\RenderPipeline\\IVolumetricCloud.cs"}}, {"pid": 12345, "tid": 2, "ts": 1756660796773818, "dur": 98, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core@14.0.11\\Runtime\\RenderPipeline\\RenderPipelineResources.cs"}}, {"pid": 12345, "tid": 2, "ts": 1756660796774270, "dur": 880, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core@14.0.11\\Runtime\\Textures\\Texture2DAtlas.cs"}}, {"pid": 12345, "tid": 2, "ts": 1756660796775192, "dur": 341, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core@14.0.11\\Runtime\\Textures\\TextureXR.cs"}}, {"pid": 12345, "tid": 2, "ts": 1756660796775786, "dur": 215, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core@14.0.11\\Runtime\\Utilities\\CoreMatrixUtils.cs"}}, {"pid": 12345, "tid": 2, "ts": 1756660796776002, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core@14.0.11\\Runtime\\Utilities\\CoreRenderPipelinePreferences.cs"}}, {"pid": 12345, "tid": 2, "ts": 1756660796757338, "dur": 19756, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1756660796777095, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756660796777253, "dur": 114, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1756660796778093, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core@14.0.11\\Editor\\Analytics\\VolumeProfileUsageAnalytic.cs"}}, {"pid": 12345, "tid": 2, "ts": 1756660796778149, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core@14.0.11\\Editor\\AssemblyInfo.cs"}}, {"pid": 12345, "tid": 2, "ts": 1756660796778246, "dur": 64, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core@14.0.11\\Editor\\BuildTargetExtensions.cs"}}, {"pid": 12345, "tid": 2, "ts": 1756660796778311, "dur": 80, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core@14.0.11\\Editor\\Camera\\CameraUI.Drawers.cs"}}, {"pid": 12345, "tid": 2, "ts": 1756660796779295, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core@14.0.11\\Editor\\EditorPrefBoolFlags.cs"}}, {"pid": 12345, "tid": 2, "ts": 1756660796781387, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core@14.0.11\\Editor\\Material\\AssetReimportUtils.cs"}}, {"pid": 12345, "tid": 2, "ts": 1756660796781581, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core@14.0.11\\Editor\\Material\\MaterialHeaderScopeList.cs"}}, {"pid": 12345, "tid": 2, "ts": 1756660796783066, "dur": 2261, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core@14.0.11\\Editor\\Volume\\Drawers\\Vector4ParameterDrawer.cs"}}, {"pid": 12345, "tid": 2, "ts": 1756660796785328, "dur": 66, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core@14.0.11\\Editor\\Volume\\SerializedDataParameter.cs"}}, {"pid": 12345, "tid": 2, "ts": 1756660796777417, "dur": 8419, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1756660796785837, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756660796785997, "dur": 93, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1756660796787296, "dur": 294, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.0.11\\Editor\\Data\\Graphs\\ColorShaderProperty.cs"}}, {"pid": 12345, "tid": 2, "ts": 1756660796787592, "dur": 169, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.0.11\\Editor\\Data\\Graphs\\CubemapInputMaterialSlot.cs"}}, {"pid": 12345, "tid": 2, "ts": 1756660796787763, "dur": 210, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.0.11\\Editor\\Data\\Graphs\\CubemapMaterialSlot.cs"}}, {"pid": 12345, "tid": 2, "ts": 1756660796788513, "dur": 79, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.0.11\\Editor\\Data\\Graphs\\GroupData.cs"}}, {"pid": 12345, "tid": 2, "ts": 1756660796788947, "dur": 1051, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.0.11\\Editor\\Data\\Graphs\\MatrixShaderProperty.cs"}}, {"pid": 12345, "tid": 2, "ts": 1756660796790126, "dur": 1088, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.0.11\\Editor\\Data\\Graphs\\PositionMaterialSlot.cs"}}, {"pid": 12345, "tid": 2, "ts": 1756660796791346, "dur": 625, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.0.11\\Editor\\Data\\Graphs\\SamplerStateMaterialSlot.cs"}}, {"pid": 12345, "tid": 2, "ts": 1756660796792212, "dur": 232, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.0.11\\Editor\\Data\\Graphs\\SerializableTextureArray.cs"}}, {"pid": 12345, "tid": 2, "ts": 1756660796795548, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.0.11\\Editor\\Data\\Legacy\\SlotReference0.cs"}}, {"pid": 12345, "tid": 2, "ts": 1756660796795685, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.0.11\\Editor\\Data\\Legacy\\StickyNoteData0.cs"}}, {"pid": 12345, "tid": 2, "ts": 1756660796795783, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.0.11\\Editor\\Data\\Legacy\\VisualEffectMasterNode1.cs"}}, {"pid": 12345, "tid": 2, "ts": 1756660796795838, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.0.11\\Editor\\Data\\Nodes\\AbstractMaterialNode.cs"}}, {"pid": 12345, "tid": 2, "ts": 1756660796797341, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.0.11\\Editor\\Data\\Nodes\\Input\\Basic\\Vector4Node.cs"}}, {"pid": 12345, "tid": 2, "ts": 1756660796797514, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.0.11\\Editor\\Data\\Nodes\\Input\\Geometry\\NormalVectorNode.cs"}}, {"pid": 12345, "tid": 2, "ts": 1756660796797942, "dur": 103, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.0.11\\Editor\\Data\\Nodes\\Input\\Gradient\\SampleGradientNode.cs"}}, {"pid": 12345, "tid": 2, "ts": 1756660796798688, "dur": 222, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.0.11\\Editor\\Data\\Nodes\\Input\\Scene\\ScreenNode.cs"}}, {"pid": 12345, "tid": 2, "ts": 1756660796799183, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.0.11\\Editor\\Data\\Nodes\\Input\\Texture\\SampleTexture2DArrayNode.cs"}}, {"pid": 12345, "tid": 2, "ts": 1756660796799423, "dur": 156, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.0.11\\Editor\\Data\\Nodes\\Input\\Texture\\Texture2DArrayAssetNode.cs"}}, {"pid": 12345, "tid": 2, "ts": 1756660796799882, "dur": 88, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.0.11\\Editor\\Data\\Nodes\\Math\\Advanced\\LogNode.cs"}}, {"pid": 12345, "tid": 2, "ts": 1756660796800048, "dur": 135, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.0.11\\Editor\\Data\\Nodes\\Math\\Advanced\\NormalizeNode.cs"}}, {"pid": 12345, "tid": 2, "ts": 1756660796800227, "dur": 311, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.0.11\\Editor\\Data\\Nodes\\Math\\Advanced\\ReciprocalNode.cs"}}, {"pid": 12345, "tid": 2, "ts": 1756660796800540, "dur": 211, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.0.11\\Editor\\Data\\Nodes\\Math\\Advanced\\ReciprocalSquareRootNode.cs"}}, {"pid": 12345, "tid": 2, "ts": 1756660796800752, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.0.11\\Editor\\Data\\Nodes\\Math\\Basic\\AddNode.cs"}}, {"pid": 12345, "tid": 2, "ts": 1756660796800804, "dur": 131, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.0.11\\Editor\\Data\\Nodes\\Math\\Basic\\DivideNode.cs"}}, {"pid": 12345, "tid": 2, "ts": 1756660796801269, "dur": 164, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.0.11\\Editor\\Data\\Nodes\\Math\\Interpolation\\SmoothstepNode.cs"}}, {"pid": 12345, "tid": 2, "ts": 1756660796801435, "dur": 211, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.0.11\\Editor\\Data\\Nodes\\Math\\Matrix\\MatrixConstructionNode.cs"}}, {"pid": 12345, "tid": 2, "ts": 1756660796801647, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.0.11\\Editor\\Data\\Nodes\\Math\\Matrix\\MatrixDeterminantNode.cs"}}, {"pid": 12345, "tid": 2, "ts": 1756660796802703, "dur": 259, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.0.11\\Editor\\Data\\Nodes\\Math\\Vector\\CrossProductNode.cs"}}, {"pid": 12345, "tid": 2, "ts": 1756660796802963, "dur": 235, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.0.11\\Editor\\Data\\Nodes\\Math\\Vector\\DistanceNode.cs"}}, {"pid": 12345, "tid": 2, "ts": 1756660796803199, "dur": 141, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.0.11\\Editor\\Data\\Nodes\\Math\\Vector\\DotProductNode.cs"}}, {"pid": 12345, "tid": 2, "ts": 1756660796803348, "dur": 826, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.0.11\\Editor\\Data\\Nodes\\Math\\Vector\\FresnelEffectNode.cs"}}, {"pid": 12345, "tid": 2, "ts": 1756660796804176, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.0.11\\Editor\\Data\\Nodes\\Math\\Vector\\ProjectionNode.cs"}}, {"pid": 12345, "tid": 2, "ts": 1756660796806073, "dur": 442, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.0.11\\Editor\\Data\\Nodes\\UV\\RotateNode.cs"}}, {"pid": 12345, "tid": 2, "ts": 1756660796807786, "dur": 89, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.0.11\\Editor\\Drawing\\Colors\\CustomColorData.cs"}}, {"pid": 12345, "tid": 2, "ts": 1756660796814541, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.0.11\\Editor\\Generation\\OutputMetadata.cs"}}, {"pid": 12345, "tid": 2, "ts": 1756660796786133, "dur": 31640, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1756660796817774, "dur": 4051, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756660796821899, "dur": 104, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1756660796822391, "dur": 185, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program Files (x86)\\Unity\\Unity 3D\\2022.3.33f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Microsoft.CSharp.dll"}}, {"pid": 12345, "tid": 2, "ts": 1756660796822692, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@14.0.11\\Editor\\2D\\CinemachineUniversalPixelPerfectEditor.cs"}}, {"pid": 12345, "tid": 2, "ts": 1756660796822746, "dur": 544, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@14.0.11\\Editor\\2D\\CompositeShadowCaster2DEditor.cs"}}, {"pid": 12345, "tid": 2, "ts": 1756660796823292, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@14.0.11\\Editor\\2D\\Converter\\BuiltInToURP2DConverterContainer.cs"}}, {"pid": 12345, "tid": 2, "ts": 1756660796824625, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@14.0.11\\Editor\\2D\\ShapeEditor\\EditablePath\\MultipleEditablePathController.cs"}}, {"pid": 12345, "tid": 2, "ts": 1756660796826972, "dur": 81, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@14.0.11\\Editor\\Camera\\UniversalRenderPipelineCameraUI.Skin.cs"}}, {"pid": 12345, "tid": 2, "ts": 1756660796827998, "dur": 67, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@14.0.11\\Editor\\GlobalSettings\\UniversalGlobalSettingsCreator.cs"}}, {"pid": 12345, "tid": 2, "ts": 1756660796829252, "dur": 69, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@14.0.11\\Editor\\ShaderGraph\\AssetCallbacks\\CreateUnlitShaderGraph.cs"}}, {"pid": 12345, "tid": 2, "ts": 1756660796830019, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@14.0.11\\Editor\\ShaderGUI\\Shaders\\ParticlesLitShader.cs"}}, {"pid": 12345, "tid": 2, "ts": 1756660796830083, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@14.0.11\\Editor\\ShaderGUI\\Shaders\\ParticlesSimpleLitShader.cs"}}, {"pid": 12345, "tid": 2, "ts": 1756660796830376, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@14.0.11\\Editor\\ShaderGUI\\ShadingModels\\SimpleLitGUI.cs"}}, {"pid": 12345, "tid": 2, "ts": 1756660796822052, "dur": 9234, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1756660796831287, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756660796831433, "dur": 90, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1756660796831524, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756660796831638, "dur": 347, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1756660796831986, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756660796832096, "dur": 674, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756660796832771, "dur": 666, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756660796833437, "dur": 689, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756660796834127, "dur": 680, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756660796834808, "dur": 665, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756660796835473, "dur": 702, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756660796836343, "dur": 696, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756660796837042, "dur": 669, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756660796837711, "dur": 1164, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756660796839396, "dur": 995, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.8\\Editor\\VisualScripting.Core\\Plugin\\Migrations\\Migration_1_2_2_to_1_2_3.cs"}}, {"pid": 12345, "tid": 2, "ts": 1756660796838875, "dur": 1732, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756660796840607, "dur": 688, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756660796842394, "dur": 4636, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756660796847030, "dur": 49440, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756660796896472, "dur": 1217, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756660796717334, "dur": 195, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program Files (x86)\\Unity\\Unity 3D\\2022.3.33f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityConnectModule.dll"}}, {"pid": 12345, "tid": 3, "ts": 1756660796717310, "dur": 222, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_CEBA40613B934AEB.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1756660796718189, "dur": 155, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756660796718344, "dur": 581, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_D67657A67A1FD2C7.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1756660796719809, "dur": 758, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program Files (x86)\\Unity\\Unity 3D\\2022.3.33f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ParticleSystemModule.dll"}}, {"pid": 12345, "tid": 3, "ts": 1756660796719807, "dur": 763, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_CAEEA24C4C6C83FB.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1756660796720570, "dur": 1073, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756660796721656, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756660796721841, "dur": 721, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 3, "ts": 1756660796722603, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1756660796722654, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756660796722749, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1756660796722821, "dur": 1846, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756660796724688, "dur": 172, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756660796724873, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756660796724968, "dur": 86, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 3, "ts": 1756660796725055, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756660796725166, "dur": 506, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756660796725685, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756660796725820, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756660796727092, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1756660796727166, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756660796727293, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756660796727353, "dur": 191, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1756660796727545, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756660796727667, "dur": 989, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.rsp2"}}, {"pid": 12345, "tid": 3, "ts": 1756660796728665, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756660796728919, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756660796729218, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756660796729592, "dur": 83, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 3, "ts": 1756660796730070, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756660796730149, "dur": 379, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756660796730560, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756660796731418, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756660796731747, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756660796732461, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756660796732730, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756660796733525, "dur": 198, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756660796733751, "dur": 503, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756660796734260, "dur": 83, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/15394042617203071315.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1756660796734344, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756660796734415, "dur": 1225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756660796735641, "dur": 733, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756660796736374, "dur": 840, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756660796737214, "dur": 750, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756660796737965, "dur": 754, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756660796738719, "dur": 715, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756660796740015, "dur": 720, "ph": "X", "name": "File", "args": {"detail": "Assets\\Plugins\\Infinity Code\\Ultimate Editor Enhancer\\Scripts\\Editor\\Prefs\\Main.Prefs.cs"}}, {"pid": 12345, "tid": 3, "ts": 1756660796739434, "dur": 2321, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756660796741756, "dur": 1509, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756660796743265, "dur": 712, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756660796743977, "dur": 656, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756660796744768, "dur": 555, "ph": "X", "name": "File", "args": {"detail": "Assets\\Plugins\\Infinity Code\\Ultimate Editor Enhancer\\Scripts\\Editor\\Behaviours\\CollectionSelector.cs"}}, {"pid": 12345, "tid": 3, "ts": 1756660796744633, "dur": 1401, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756660796746034, "dur": 680, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756660796746714, "dur": 916, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756660796747630, "dur": 760, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756660796748391, "dur": 936, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756660796749327, "dur": 662, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756660796749989, "dur": 669, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756660796750659, "dur": 619, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756660796751279, "dur": 658, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756660796751938, "dur": 963, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756660796752901, "dur": 724, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756660796753625, "dur": 664, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756660796754335, "dur": 654, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756660796754990, "dur": 954, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756660796756153, "dur": 1624, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.textmeshpro@3.0.9\\Scripts\\Editor\\GlyphRectPropertyDrawer.cs"}}, {"pid": 12345, "tid": 3, "ts": 1756660796755945, "dur": 2379, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756660796758326, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1756660796758790, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.mathematics@1.2.6\\Unity.Mathematics\\bool2.gen.cs"}}, {"pid": 12345, "tid": 3, "ts": 1756660796758968, "dur": 487, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.mathematics@1.2.6\\Unity.Mathematics\\bool3.gen.cs"}}, {"pid": 12345, "tid": 3, "ts": 1756660796759456, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.mathematics@1.2.6\\Unity.Mathematics\\bool3x2.gen.cs"}}, {"pid": 12345, "tid": 3, "ts": 1756660796759519, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.mathematics@1.2.6\\Unity.Mathematics\\bool3x3.gen.cs"}}, {"pid": 12345, "tid": 3, "ts": 1756660796759571, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.mathematics@1.2.6\\Unity.Mathematics\\bool3x4.gen.cs"}}, {"pid": 12345, "tid": 3, "ts": 1756660796759668, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.mathematics@1.2.6\\Unity.Mathematics\\bool4x2.gen.cs"}}, {"pid": 12345, "tid": 3, "ts": 1756660796759847, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.mathematics@1.2.6\\Unity.Mathematics\\double2x2.gen.cs"}}, {"pid": 12345, "tid": 3, "ts": 1756660796760298, "dur": 187, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.mathematics@1.2.6\\Unity.Mathematics\\float2x2.gen.cs"}}, {"pid": 12345, "tid": 3, "ts": 1756660796760486, "dur": 429, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.mathematics@1.2.6\\Unity.Mathematics\\float2x3.gen.cs"}}, {"pid": 12345, "tid": 3, "ts": 1756660796760988, "dur": 84, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.mathematics@1.2.6\\Unity.Mathematics\\float3x2.gen.cs"}}, {"pid": 12345, "tid": 3, "ts": 1756660796761073, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.mathematics@1.2.6\\Unity.Mathematics\\float3x3.gen.cs"}}, {"pid": 12345, "tid": 3, "ts": 1756660796761173, "dur": 80, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.mathematics@1.2.6\\Unity.Mathematics\\float4.gen.cs"}}, {"pid": 12345, "tid": 3, "ts": 1756660796761330, "dur": 297, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.mathematics@1.2.6\\Unity.Mathematics\\float4x4.gen.cs"}}, {"pid": 12345, "tid": 3, "ts": 1756660796762304, "dur": 317, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.mathematics@1.2.6\\Unity.Mathematics\\math.cs"}}, {"pid": 12345, "tid": 3, "ts": 1756660796763596, "dur": 7605, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.mathematics@1.2.6\\Unity.Mathematics\\uint3x2.gen.cs"}}, {"pid": 12345, "tid": 3, "ts": 1756660796771203, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.mathematics@1.2.6\\Unity.Mathematics\\uint3x3.gen.cs"}}, {"pid": 12345, "tid": 3, "ts": 1756660796771379, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.mathematics@1.2.6\\Unity.Mathematics\\uint4x3.gen.cs"}}, {"pid": 12345, "tid": 3, "ts": 1756660796758454, "dur": 13016, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1756660796771471, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756660796771586, "dur": 2730, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756660796774340, "dur": 106, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1756660796774448, "dur": 143, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756660796775101, "dur": 780, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.mathematics@1.2.6\\Unity.Mathematics.Editor\\MatrixDrawer.cs"}}, {"pid": 12345, "tid": 3, "ts": 1756660796774598, "dur": 1393, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1756660796775992, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756660796778090, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.textmeshpro@3.0.9\\Scripts\\Runtime\\TMP_SpriteCharacter.cs"}}, {"pid": 12345, "tid": 3, "ts": 1756660796778145, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.textmeshpro@3.0.9\\Scripts\\Runtime\\TMP_SpriteGlyph.cs"}}, {"pid": 12345, "tid": 3, "ts": 1756660796778243, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.textmeshpro@3.0.9\\Scripts\\Runtime\\TMP_StyleSheet.cs"}}, {"pid": 12345, "tid": 3, "ts": 1756660796776144, "dur": 2603, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1756660796778748, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756660796778869, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1756660796778999, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1756660796779117, "dur": 394, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1756660796779511, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756660796779632, "dur": 445, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1756660796780078, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756660796780283, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1756660796781277, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@14.0.11\\Runtime\\2D\\Passes\\Utility\\LayerUtility.cs"}}, {"pid": 12345, "tid": 3, "ts": 1756660796783280, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@14.0.11\\Runtime\\Decal\\ScreenSpace\\DecalGBufferRenderPass.cs"}}, {"pid": 12345, "tid": 3, "ts": 1756660796785342, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@14.0.11\\Runtime\\Passes\\InvokeOnRenderObjectCallbackPass.cs"}}, {"pid": 12345, "tid": 3, "ts": 1756660796787337, "dur": 128, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@14.0.11\\Runtime\\XR\\XRPassUniversal.cs"}}, {"pid": 12345, "tid": 3, "ts": 1756660796787467, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@14.0.11\\Runtime\\XR\\XRSystemUniversal.cs"}}, {"pid": 12345, "tid": 3, "ts": 1756660796780409, "dur": 7118, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1756660796787528, "dur": 163, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756660796787705, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756660796787839, "dur": 583, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 3, "ts": 1756660796788476, "dur": 109, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756660796789345, "dur": 1530421, "ph": "X", "name": "ILPP-Configuration", "args": {"detail": "Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 4, "ts": 1756660796717409, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_EA8E1315F893D5F7.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1756660796717643, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program Files (x86)\\Unity\\Unity 3D\\2022.3.33f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TLSModule.dll"}}, {"pid": 12345, "tid": 4, "ts": 1756660796717638, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_E5A8879E05CAF719.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1756660796717741, "dur": 87, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program Files (x86)\\Unity\\Unity 3D\\2022.3.33f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TextCoreTextEngineModule.dll"}}, {"pid": 12345, "tid": 4, "ts": 1756660796717739, "dur": 91, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_71DCE14D58EC7CB1.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1756660796717831, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756660796717907, "dur": 101, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program Files (x86)\\Unity\\Unity 3D\\2022.3.33f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.SubsystemsModule.dll"}}, {"pid": 12345, "tid": 4, "ts": 1756660796717906, "dur": 105, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_4123BE084867BADA.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1756660796718011, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756660796718127, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756660796718191, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_3B036F4287C9841F.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1756660796718251, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756660796718378, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ScreenCaptureModule.dll_EE6422BA6596CB08.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1756660796718512, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756660796718792, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756660796720129, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program Files (x86)\\Unity\\Unity 3D\\2022.3.33f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestTextureModule.dll"}}, {"pid": 12345, "tid": 4, "ts": 1756660796720128, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_283DA3D7A0C52561.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1756660796720272, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756660796720450, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756660796720522, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program Files (x86)\\Unity\\Unity 3D\\2022.3.33f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VRModule.dll"}}, {"pid": 12345, "tid": 4, "ts": 1756660796720577, "dur": 79, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program Files (x86)\\Unity\\Unity 3D\\2022.3.33f1c1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 4, "ts": 1756660796720521, "dur": 136, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_0272E73F52A442C2.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1756660796721145, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Newtonsoft.Json.dll_88EDA310933505FB.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1756660796721446, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756660796721518, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756660796721754, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1756660796721964, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1756660796722238, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Assets\\Plugins\\Demigiant\\DOTween\\Editor\\DOTweenEditor.dll"}}, {"pid": 12345, "tid": 4, "ts": 1756660796722536, "dur": 5237, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program Files (x86)\\Unity\\Unity 3D\\2022.3.33f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Numerics.dll"}}, {"pid": 12345, "tid": 4, "ts": 1756660796727916, "dur": 107, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program Files (x86)\\Unity\\Unity 3D\\2022.3.33f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.FileSystem.dll"}}, {"pid": 12345, "tid": 4, "ts": 1756660796728025, "dur": 171, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program Files (x86)\\Unity\\Unity 3D\\2022.3.33f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.FileSystem.DriveInfo.dll"}}, {"pid": 12345, "tid": 4, "ts": 1756660796728201, "dur": 1059, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program Files (x86)\\Unity\\Unity 3D\\2022.3.33f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.IsolatedStorage.dll"}}, {"pid": 12345, "tid": 4, "ts": 1756660796729321, "dur": 322, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program Files (x86)\\Unity\\Unity 3D\\2022.3.33f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.NetworkInformation.dll"}}, {"pid": 12345, "tid": 4, "ts": 1756660796729653, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program Files (x86)\\Unity\\Unity 3D\\2022.3.33f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.WebHeaderCollection.dll"}}, {"pid": 12345, "tid": 4, "ts": 1756660796729717, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program Files (x86)\\Unity\\Unity 3D\\2022.3.33f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.WebSockets.Client.dll"}}, {"pid": 12345, "tid": 4, "ts": 1756660796729778, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program Files (x86)\\Unity\\Unity 3D\\2022.3.33f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.WebSockets.dll"}}, {"pid": 12345, "tid": 4, "ts": 1756660796729829, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program Files (x86)\\Unity\\Unity 3D\\2022.3.33f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Numerics.Vectors.dll"}}, {"pid": 12345, "tid": 4, "ts": 1756660796730214, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program Files (x86)\\Unity\\Unity 3D\\2022.3.33f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Resources.Reader.dll"}}, {"pid": 12345, "tid": 4, "ts": 1756660796730578, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program Files (x86)\\Unity\\Unity 3D\\2022.3.33f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.Numerics.dll"}}, {"pid": 12345, "tid": 4, "ts": 1756660796730630, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program Files (x86)\\Unity\\Unity 3D\\2022.3.33f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.Serialization.Formatters.dll"}}, {"pid": 12345, "tid": 4, "ts": 1756660796730687, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program Files (x86)\\Unity\\Unity 3D\\2022.3.33f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.Serialization.Json.dll"}}, {"pid": 12345, "tid": 4, "ts": 1756660796730794, "dur": 709, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program Files (x86)\\Unity\\Unity 3D\\2022.3.33f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.Serialization.Xml.dll"}}, {"pid": 12345, "tid": 4, "ts": 1756660796731661, "dur": 84, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program Files (x86)\\Unity\\Unity 3D\\2022.3.33f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Xml.XDocument.dll"}}, {"pid": 12345, "tid": 4, "ts": 1756660796731746, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program Files (x86)\\Unity\\Unity 3D\\2022.3.33f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Xml.XmlDocument.dll"}}, {"pid": 12345, "tid": 4, "ts": 1756660796731801, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program Files (x86)\\Unity\\Unity 3D\\2022.3.33f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Xml.XmlSerializer.dll"}}, {"pid": 12345, "tid": 4, "ts": 1756660796731867, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program Files (x86)\\Unity\\Unity 3D\\2022.3.33f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Xml.XPath.dll"}}, {"pid": 12345, "tid": 4, "ts": 1756660796731922, "dur": 75, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program Files (x86)\\Unity\\Unity 3D\\2022.3.33f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Xml.XPath.XDocument.dll"}}, {"pid": 12345, "tid": 4, "ts": 1756660796732007, "dur": 66, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.9.2\\Lib\\Editor\\log4netPlastic.dll"}}, {"pid": 12345, "tid": 4, "ts": 1756660796732077, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.9.2\\Lib\\Editor\\unityplastic.dll"}}, {"pid": 12345, "tid": 4, "ts": 1756660796732138, "dur": 66, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.nuget.newtonsoft-json@3.2.1\\Runtime\\Newtonsoft.Json.dll"}}, {"pid": 12345, "tid": 4, "ts": 1756660796732213, "dur": 329, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@14.0.11\\Editor\\ShaderGUI\\MaterialAssemblyReference\\RawRenderQueue.cs"}}, {"pid": 12345, "tid": 4, "ts": 1756660796732591, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\EventData\\AxisEventData.cs"}}, {"pid": 12345, "tid": 4, "ts": 1756660796732732, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\EventHandle.cs"}}, {"pid": 12345, "tid": 4, "ts": 1756660796733537, "dur": 204, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Animation\\CoroutineTween.cs"}}, {"pid": 12345, "tid": 4, "ts": 1756660796733742, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\AnimationTriggers.cs"}}, {"pid": 12345, "tid": 4, "ts": 1756660796734550, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Layout\\CanvasScaler.cs"}}, {"pid": 12345, "tid": 4, "ts": 1756660796734708, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Layout\\HorizontalOrVerticalLayoutGroup.cs"}}, {"pid": 12345, "tid": 4, "ts": 1756660796722088, "dur": 13966, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1756660796736055, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756660796736272, "dur": 937, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756660796737209, "dur": 837, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756660796738047, "dur": 739, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756660796739297, "dur": 2575, "ph": "X", "name": "File", "args": {"detail": "Assets\\Plugins\\Infinity Code\\Ultimate Editor Enhancer\\Scripts\\Editor\\Prefs\\QuickAccessBar.Prefs.cs"}}, {"pid": 12345, "tid": 4, "ts": 1756660796738787, "dur": 3376, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756660796742164, "dur": 1186, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756660796743351, "dur": 707, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756660796744059, "dur": 847, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756660796744906, "dur": 1090, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756660796745997, "dur": 666, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756660796746663, "dur": 969, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756660796747632, "dur": 796, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756660796748429, "dur": 1315, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756660796749745, "dur": 1227, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756660796750972, "dur": 662, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756660796751635, "dur": 994, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756660796752630, "dur": 989, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756660796753620, "dur": 665, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756660796754819, "dur": 513, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@1.7.7\\Editor\\CustomEditors\\MarkerTrackEditor.cs"}}, {"pid": 12345, "tid": 4, "ts": 1756660796754330, "dur": 1169, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756660796755981, "dur": 604, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.textmeshpro@3.0.9\\Scripts\\Editor\\TMP_ColorGradientAssetMenu.cs"}}, {"pid": 12345, "tid": 4, "ts": 1756660796755499, "dur": 1444, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756660796757388, "dur": 70, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program Files (x86)\\Unity\\Unity 3D\\2022.3.33f1c1\\Editor\\Data\\PlaybackEngines\\WindowsStandaloneSupport\\UnityEditor.WindowsStandalone.Extensions.dll"}}, {"pid": 12345, "tid": 4, "ts": 1756660796757463, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.burst@1.8.15\\Runtime\\BurstCompileAttribute.cs"}}, {"pid": 12345, "tid": 4, "ts": 1756660796757742, "dur": 150, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.burst@1.8.15\\Runtime\\CompilerServices\\Aliasing.cs"}}, {"pid": 12345, "tid": 4, "ts": 1756660796758925, "dur": 595, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.burst@1.8.15\\Runtime\\Intrinsics\\x86\\Avx2.cs"}}, {"pid": 12345, "tid": 4, "ts": 1756660796759524, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.burst@1.8.15\\Runtime\\Intrinsics\\x86\\Bmi1.cs"}}, {"pid": 12345, "tid": 4, "ts": 1756660796759575, "dur": 4044, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.burst@1.8.15\\Runtime\\Intrinsics\\x86\\Bmi2.cs"}}, {"pid": 12345, "tid": 4, "ts": 1756660796763740, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.burst@1.8.15\\Runtime\\Intrinsics\\x86\\Fma.cs"}}, {"pid": 12345, "tid": 4, "ts": 1756660796756944, "dur": 7189, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1756660796764135, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756660796764298, "dur": 98, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1756660796764909, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.searcher@4.9.2\\Editor\\Searcher\\SearcherAdapter.cs"}}, {"pid": 12345, "tid": 4, "ts": 1756660796764967, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.searcher@4.9.2\\Editor\\Searcher\\SearcherControl.cs"}}, {"pid": 12345, "tid": 4, "ts": 1756660796764434, "dur": 807, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1756660796765241, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756660796765353, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1756660796765489, "dur": 372, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1756660796765861, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756660796765990, "dur": 89, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1756660796766121, "dur": 525, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1756660796766647, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756660796766850, "dur": 89, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1756660796766977, "dur": 1017, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1756660796767995, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756660796768098, "dur": 83, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1756660796768221, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1756660796788909, "dur": 94, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@1.7.7\\Runtime\\Activation\\ActivationPlayableAsset.cs"}}, {"pid": 12345, "tid": 4, "ts": 1756660796789047, "dur": 81, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@1.7.7\\Runtime\\Animation\\AnimationOutputWeightProcessor.cs"}}, {"pid": 12345, "tid": 4, "ts": 1756660796789130, "dur": 157, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@1.7.7\\Runtime\\Animation\\AnimationPlayableAsset.cs"}}, {"pid": 12345, "tid": 4, "ts": 1756660796789288, "dur": 474, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@1.7.7\\Runtime\\Animation\\AnimationPreviewUpdateCallback.cs"}}, {"pid": 12345, "tid": 4, "ts": 1756660796789763, "dur": 109, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@1.7.7\\Runtime\\Animation\\AnimationTrack.cs"}}, {"pid": 12345, "tid": 4, "ts": 1756660796789925, "dur": 940, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@1.7.7\\Runtime\\AssetUpgrade\\AnimationPlayableAssetUpgrade.cs"}}, {"pid": 12345, "tid": 4, "ts": 1756660796791105, "dur": 102, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@1.7.7\\Runtime\\Audio\\AudioClipProperties.cs"}}, {"pid": 12345, "tid": 4, "ts": 1756660796791515, "dur": 802, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@1.7.7\\Runtime\\Audio\\AudioPlayableAsset.cs"}}, {"pid": 12345, "tid": 4, "ts": 1756660796792359, "dur": 75, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@1.7.7\\Runtime\\ClipCaps.cs"}}, {"pid": 12345, "tid": 4, "ts": 1756660796793118, "dur": 70, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@1.7.7\\Runtime\\Events\\Signals\\SignalReceiver.cs"}}, {"pid": 12345, "tid": 4, "ts": 1756660796768333, "dur": 26174, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1756660796794508, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756660796794718, "dur": 93, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1756660796795550, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.9.2\\Editor\\AssetMenu\\HierarchyViewAssetMenu.cs"}}, {"pid": 12345, "tid": 4, "ts": 1756660796795683, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.9.2\\Editor\\AssetMenu\\ProjectViewAssetSelection.cs"}}, {"pid": 12345, "tid": 4, "ts": 1756660796795779, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.9.2\\Editor\\AssetOverlays\\AssetStatus.cs"}}, {"pid": 12345, "tid": 4, "ts": 1756660796795834, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.9.2\\Editor\\AssetOverlays\\Cache\\AssetStatusCache.cs"}}, {"pid": 12345, "tid": 4, "ts": 1756660796797129, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.9.2\\Editor\\CloudDrive\\RefreshAsset.cs"}}, {"pid": 12345, "tid": 4, "ts": 1756660796798662, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.9.2\\Editor\\Developer\\UpdateReport\\UpdateReportDialog.cs"}}, {"pid": 12345, "tid": 4, "ts": 1756660796798721, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.9.2\\Editor\\Developer\\UpdateReport\\UpdateReportLineListViewItem.cs"}}, {"pid": 12345, "tid": 4, "ts": 1756660796799884, "dur": 666, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.9.2\\Editor\\PackageInfo.cs"}}, {"pid": 12345, "tid": 4, "ts": 1756660796800551, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.9.2\\Editor\\ParentWindow.cs"}}, {"pid": 12345, "tid": 4, "ts": 1756660796800607, "dur": 256, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.9.2\\Editor\\PlasticApp.cs"}}, {"pid": 12345, "tid": 4, "ts": 1756660796800864, "dur": 124, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.9.2\\Editor\\ProjectLoadedCounter.cs"}}, {"pid": 12345, "tid": 4, "ts": 1756660796801303, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.9.2\\Editor\\Settings\\ShelveAndSwitchOptionsFoldout.cs"}}, {"pid": 12345, "tid": 4, "ts": 1756660796801402, "dur": 89, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.9.2\\Editor\\StatusBar\\GUIContentNotification.cs"}}, {"pid": 12345, "tid": 4, "ts": 1756660796801493, "dur": 91, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.9.2\\Editor\\StatusBar\\INotificationContent.cs"}}, {"pid": 12345, "tid": 4, "ts": 1756660796801585, "dur": 70, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.9.2\\Editor\\StatusBar\\NotificationBar.cs"}}, {"pid": 12345, "tid": 4, "ts": 1756660796802597, "dur": 74, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.9.2\\Editor\\UI\\DrawCopyableLabel.cs"}}, {"pid": 12345, "tid": 4, "ts": 1756660796802712, "dur": 245, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.9.2\\Editor\\UI\\DrawSplitter.cs"}}, {"pid": 12345, "tid": 4, "ts": 1756660796802959, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.9.2\\Editor\\UI\\DrawStaticElement.cs"}}, {"pid": 12345, "tid": 4, "ts": 1756660796803091, "dur": 116, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.9.2\\Editor\\UI\\DropDownTextField.cs"}}, {"pid": 12345, "tid": 4, "ts": 1756660796803208, "dur": 98, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.9.2\\Editor\\UI\\EditorDispatcher.cs"}}, {"pid": 12345, "tid": 4, "ts": 1756660796803307, "dur": 95, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.9.2\\Editor\\UI\\EditorProgressBar.cs"}}, {"pid": 12345, "tid": 4, "ts": 1756660796803403, "dur": 112, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.9.2\\Editor\\UI\\EditorProgressControls.cs"}}, {"pid": 12345, "tid": 4, "ts": 1756660796804167, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.9.2\\Editor\\UI\\Progress\\DrawProgressForDialogs.cs"}}, {"pid": 12345, "tid": 4, "ts": 1756660796806105, "dur": 261, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.9.2\\Editor\\UVCSToolbar\\Headless\\HeadlessWorkspaceStatusChangeListener.cs"}}, {"pid": 12345, "tid": 4, "ts": 1756660796806446, "dur": 70, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.9.2\\Editor\\UVCSToolbar\\PopupWindow\\BranchesList\\BranchesTreeView.cs"}}, {"pid": 12345, "tid": 4, "ts": 1756660796807775, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.9.2\\Editor\\Views\\Changesets\\ChangesetsListView.cs"}}, {"pid": 12345, "tid": 4, "ts": 1756660796794851, "dur": 18348, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1756660796813201, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756660796813321, "dur": 99, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1756660796814484, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.8\\Editor\\VisualScripting.Core\\AssetBundleCreator.cs"}}, {"pid": 12345, "tid": 4, "ts": 1756660796814539, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.8\\Editor\\VisualScripting.Core\\Assignment\\Assigner.cs"}}, {"pid": 12345, "tid": 4, "ts": 1756660796822337, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.8\\Editor\\VisualScripting.Core\\Platforms\\AotStubWriterProvider.cs"}}, {"pid": 12345, "tid": 4, "ts": 1756660796822394, "dur": 20377, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.8\\Editor\\VisualScripting.Core\\Platforms\\ConstructorInfoStubWriter.cs"}}, {"pid": 12345, "tid": 4, "ts": 1756660796813461, "dur": 29698, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1756660796843161, "dur": 142, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756660796843367, "dur": 99, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1756660796843515, "dur": 641, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1756660796844157, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756660796844348, "dur": 101, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1756660796844490, "dur": 498, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1756660796844989, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756660796845175, "dur": 804, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1756660796845980, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756660796846042, "dur": 340, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1756660796846383, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756660796847035, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-firstpass.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1756660796847170, "dur": 368, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-firstpass.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1756660796847538, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756660796848147, "dur": 361, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1756660796848995, "dur": 61, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756660796849187, "dur": 1385085, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1756660798349828, "dur": 3804, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 29040, "tid": 656078, "ts": 1756660801288900, "dur": 46242, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend2.traceevents"}}, {"pid": 29040, "tid": 656078, "ts": 1756660801339768, "dur": 68, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "buildprogram0.traceevents"}}, {"pid": 29040, "tid": 656078, "ts": 1756660801340330, "dur": 27, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 29040, "tid": 656078, "ts": 1756660801335318, "dur": 4441, "ph": "X", "name": "backend2.traceevents", "args": {}}, {"pid": 29040, "tid": 656078, "ts": 1756660801339993, "dur": 336, "ph": "X", "name": "buildprogram0.traceevents", "args": {}}, {"pid": 29040, "tid": 656078, "ts": 1756660801340474, "dur": 1556, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 29040, "tid": 656078, "ts": 1756660801283670, "dur": 59869, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}