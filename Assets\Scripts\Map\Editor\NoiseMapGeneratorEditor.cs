using UnityEngine;
using UnityEditor;

/// <summary>
/// NoiseMapGenerator的自定义编辑器
/// </summary>
[CustomEditor(typeof(NoiseMapGenerator))]
public class NoiseMapGeneratorEditor : Editor
{
    private NoiseMapGenerator generator;
    private bool showNoiseSettings = true;
    private bool showRedistributionSettings = true;
    private bool showPreviewSettings = true;
    
    void OnEnable()
    {
        generator = (NoiseMapGenerator)target;
    }
    
    public override void OnInspectorGUI()
    {
        EditorGUI.BeginChangeCheck();
        
        // 标题
        EditorGUILayout.Space();
        GUIStyle titleStyle = new GUIStyle(EditorStyles.boldLabel);
        titleStyle.fontSize = 16;
        titleStyle.alignment = TextAnchor.MiddleCenter;
        EditorGUILayout.LabelField("噪声地图生成器", titleStyle);
        EditorGUILayout.Space();
        
        // 基础设置
        DrawBasicSettings();
        
        EditorGUILayout.Space();
        
        // 噪声设置
        showNoiseSettings = EditorGUILayout.Foldout(showNoiseSettings, "噪声参数设置", true);
        if (showNoiseSettings)
        {
            EditorGUI.indentLevel++;
            DrawNoiseSettings();
            EditorGUI.indentLevel--;
        }
        
        EditorGUILayout.Space();
        
        // 重分布设置
        showRedistributionSettings = EditorGUILayout.Foldout(showRedistributionSettings, "高度重分布设置", true);
        if (showRedistributionSettings)
        {
            EditorGUI.indentLevel++;
            DrawRedistributionSettings();
            EditorGUI.indentLevel--;
        }
        
        EditorGUILayout.Space();
        
        // 预览设置
        showPreviewSettings = EditorGUILayout.Foldout(showPreviewSettings, "预览和圆滑化设置", true);
        if (showPreviewSettings)
        {
            EditorGUI.indentLevel++;
            DrawPreviewSettings();
            EditorGUI.indentLevel--;
        }
        
        EditorGUILayout.Space();
        
        // 操作按钮
        DrawActionButtons();
        
        if (EditorGUI.EndChangeCheck())
        {
            serializedObject.ApplyModifiedProperties();
            
            if (generator.autoUpdate && Application.isPlaying)
            {
                generator.GenerateMap();
            }
        }
    }
    
    private void DrawBasicSettings()
    {
        EditorGUILayout.LabelField("基础设置", EditorStyles.boldLabel);
        
        SerializedProperty mapWidth = serializedObject.FindProperty("mapWidth");
        SerializedProperty mapHeight = serializedObject.FindProperty("mapHeight");
        SerializedProperty seed = serializedObject.FindProperty("seed");
        
        EditorGUILayout.PropertyField(mapWidth, new GUIContent("地图宽度"));
        EditorGUILayout.PropertyField(mapHeight, new GUIContent("地图高度"));
        
        EditorGUILayout.BeginHorizontal();
        EditorGUILayout.PropertyField(seed, new GUIContent("随机种子"));
        if (GUILayout.Button("随机", GUILayout.Width(60)))
        {
            seed.intValue = Random.Range(0, 100000);
        }
        EditorGUILayout.EndHorizontal();
    }
    
    private void DrawNoiseSettings()
    {
        SerializedProperty noiseScale = serializedObject.FindProperty("noiseScale");
        SerializedProperty octaves = serializedObject.FindProperty("octaves");
        SerializedProperty persistence = serializedObject.FindProperty("persistence");
        SerializedProperty lacunarity = serializedObject.FindProperty("lacunarity");
        SerializedProperty offset = serializedObject.FindProperty("offset");
        
        EditorGUILayout.PropertyField(noiseScale, new GUIContent("噪声缩放"));
        EditorGUILayout.PropertyField(octaves, new GUIContent("倍频程数量"));
        EditorGUILayout.PropertyField(persistence, new GUIContent("持续性"));
        EditorGUILayout.PropertyField(lacunarity, new GUIContent("间隙性"));
        EditorGUILayout.PropertyField(offset, new GUIContent("偏移量"));
        
        EditorGUILayout.Space();
        
        SerializedProperty useCustomAmplitudes = serializedObject.FindProperty("useCustomAmplitudes");
        EditorGUILayout.PropertyField(useCustomAmplitudes, new GUIContent("使用自定义振幅"));
        
        if (useCustomAmplitudes.boolValue)
        {
            EditorGUI.indentLevel++;
            SerializedProperty customAmplitudes = serializedObject.FindProperty("customAmplitudes");
            SerializedProperty normalizeAmplitudes = serializedObject.FindProperty("normalizeAmplitudes");
            
            EditorGUILayout.PropertyField(customAmplitudes, new GUIContent("自定义振幅数组"), true);
            EditorGUILayout.PropertyField(normalizeAmplitudes, new GUIContent("标准化振幅"));
            
            EditorGUILayout.BeginHorizontal();
            if (GUILayout.Button("Red Blob预设"))
            {
                customAmplitudes.arraySize = 6;
                customAmplitudes.GetArrayElementAtIndex(0).floatValue = 1f;
                customAmplitudes.GetArrayElementAtIndex(1).floatValue = 0.5f;
                customAmplitudes.GetArrayElementAtIndex(2).floatValue = 0.25f;
                customAmplitudes.GetArrayElementAtIndex(3).floatValue = 0.125f;
                customAmplitudes.GetArrayElementAtIndex(4).floatValue = 0.0625f;
                customAmplitudes.GetArrayElementAtIndex(5).floatValue = 0.03125f;
            }
            if (GUILayout.Button("细节增强预设"))
            {
                customAmplitudes.arraySize = 5;
                customAmplitudes.GetArrayElementAtIndex(0).floatValue = 1f;
                customAmplitudes.GetArrayElementAtIndex(1).floatValue = 1f/2f;
                customAmplitudes.GetArrayElementAtIndex(2).floatValue = 1f/3f;
                customAmplitudes.GetArrayElementAtIndex(3).floatValue = 1f/4f;
                customAmplitudes.GetArrayElementAtIndex(4).floatValue = 1f/5f;
            }
            EditorGUILayout.EndHorizontal();
            EditorGUI.indentLevel--;
        }
    }
    
    private void DrawRedistributionSettings()
    {
        SerializedProperty useRedistribution = serializedObject.FindProperty("useRedistribution");
        EditorGUILayout.PropertyField(useRedistribution, new GUIContent("启用高度重分布"));
        
        if (useRedistribution.boolValue)
        {
            EditorGUI.indentLevel++;
            SerializedProperty redistributionType = serializedObject.FindProperty("redistributionType");
            EditorGUILayout.PropertyField(redistributionType, new GUIContent("重分布类型"));
            
            RedistributionType type = (RedistributionType)redistributionType.enumValueIndex;
            
            switch (type)
            {
                case RedistributionType.Power:
                case RedistributionType.InversePower:
                    SerializedProperty exponent = serializedObject.FindProperty("redistributionExponent");
                    EditorGUILayout.PropertyField(exponent, new GUIContent("重分布指数"));
                    break;
                    
                case RedistributionType.Curve:
                    SerializedProperty curve = serializedObject.FindProperty("redistributionCurve");
                    EditorGUILayout.PropertyField(curve, new GUIContent("重分布曲线"));
                    break;
                    
                case RedistributionType.Terraces:
                    SerializedProperty terraceCount = serializedObject.FindProperty("terraceCount");
                    SerializedProperty terraceSmoothing = serializedObject.FindProperty("terraceSmoothing");
                    EditorGUILayout.PropertyField(terraceCount, new GUIContent("梯田数量"));
                    EditorGUILayout.PropertyField(terraceSmoothing, new GUIContent("梯田平滑度"));
                    break;
            }
            EditorGUI.indentLevel--;
        }
    }
    
    private void DrawPreviewSettings()
    {
        SerializedProperty autoUpdate = serializedObject.FindProperty("autoUpdate");
        SerializedProperty textureRenderer = serializedObject.FindProperty("textureRenderer");
        SerializedProperty textureFilterMode = serializedObject.FindProperty("textureFilterMode");
        SerializedProperty useTerrainColoring = serializedObject.FindProperty("useTerrainColoring");
        SerializedProperty colorMode = serializedObject.FindProperty("colorMode");
        
        EditorGUILayout.PropertyField(autoUpdate, new GUIContent("自动更新"));
        EditorGUILayout.PropertyField(textureRenderer, new GUIContent("纹理渲染器"));
        EditorGUILayout.PropertyField(textureFilterMode, new GUIContent("纹理过滤模式"));
        EditorGUILayout.PropertyField(useTerrainColoring, new GUIContent("使用地形着色"));
        
        if (useTerrainColoring.boolValue)
        {
            EditorGUI.indentLevel++;
            EditorGUILayout.PropertyField(colorMode, new GUIContent("着色模式"));
            EditorGUI.indentLevel--;
        }
        
        EditorGUILayout.Space();
        EditorGUILayout.LabelField("圆滑化设置", EditorStyles.boldLabel);
        
        SerializedProperty useSmoothTerrain = serializedObject.FindProperty("useSmoothTerrain");
        EditorGUILayout.PropertyField(useSmoothTerrain, new GUIContent("启用地形圆滑化"));
        
        if (useSmoothTerrain.boolValue)
        {
            EditorGUI.indentLevel++;
            SerializedProperty smoothingIterations = serializedObject.FindProperty("smoothingIterations");
            SerializedProperty smoothingStrength = serializedObject.FindProperty("smoothingStrength");
            
            EditorGUILayout.PropertyField(smoothingIterations, new GUIContent("平滑迭代次数"));
            EditorGUILayout.PropertyField(smoothingStrength, new GUIContent("平滑强度"));
            
            EditorGUILayout.BeginHorizontal();
            if (GUILayout.Button("轻度圆滑"))
            {
                smoothingIterations.intValue = 1;
                smoothingStrength.floatValue = 0.3f;
            }
            if (GUILayout.Button("中度圆滑"))
            {
                smoothingIterations.intValue = 2;
                smoothingStrength.floatValue = 0.5f;
            }
            if (GUILayout.Button("重度圆滑"))
            {
                smoothingIterations.intValue = 3;
                smoothingStrength.floatValue = 0.7f;
            }
            EditorGUILayout.EndHorizontal();
            EditorGUI.indentLevel--;
        }
    }
    
    private void DrawActionButtons()
    {
        EditorGUILayout.BeginHorizontal();
        
        if (GUILayout.Button("生成地图", GUILayout.Height(30)))
        {
            generator.GenerateMap();
        }
        
        if (GUILayout.Button("导出纹理", GUILayout.Height(30)))
        {
            ExportTexture();
        }
        
        EditorGUILayout.EndHorizontal();
        
        if (generator.GetNoiseMap() != null)
        {
            EditorGUILayout.Space();
            EditorGUILayout.HelpBox($"当前地图尺寸: {generator.GetNoiseMap().GetLength(0)} x {generator.GetNoiseMap().GetLength(1)}", MessageType.Info);
        }
    }
    
    private void ExportTexture()
    {
        if (generator.GetNoiseMap() == null)
        {
            EditorUtility.DisplayDialog("错误", "请先生成地图！", "确定");
            return;
        }
        
        string path = EditorUtility.SaveFilePanel("导出噪声纹理", "", "NoiseMap", "png");
        if (!string.IsNullOrEmpty(path))
        {
            Texture2D texture;
            if (generator.useTerrainColoring && generator.colorMode != TerrainColorMode.Grayscale)
            {
                texture = NoiseMapGenerator.TextureFromHeightMapWithTerrain(generator.GetNoiseMap(), generator.colorMode, generator.textureFilterMode);
            }
            else
            {
                texture = NoiseMapGenerator.TextureFromHeightMap(generator.GetNoiseMap(), generator.textureFilterMode);
            }
            
            byte[] bytes = texture.EncodeToPNG();
            System.IO.File.WriteAllBytes(path, bytes);
            
            EditorUtility.DisplayDialog("成功", "纹理已导出到: " + path, "确定");
        }
    }
}
